APP_NAME=OcMed
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
APP_COMPANY=OcMed

TENANCY_CENTRAL_DOMAIN=

QI=
VE=
MASTER_PASSWORD=

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=forge
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

DIGITALOCEAN_SPACES_KEY=
DIGITALOCEAN_SPACES_SECRET=
DIGITALOCEAN_SPACES_ENDPOINT=
DIGITALOCEAN_SPACES_REGION=
DIGITALOCEAN_SPACES_BUCKET=
DIGITALOCEAN_RECEIVABLE_EXPORT_DOWNLOAD_URL=https://ocmed.sfo3.digitaloceanspaces.com/
DIGITALOCEAN_SPACES_IMAGES_URL=https://ocmed.sfo3.digitaloceanspaces.com/images/

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Brasil API
BRASIL_API_API_V1_URL=https://brasilapi.com.br/api/

# CLM Online
CLM_ONLINE_API_URL=https://webapiclmonline.com.br:8443/api/

# ERPFlex
ERP_FLEX_CONNECTION_MODE=local
ERP_FLEX_API_NEW_URL=https://sistema.erpflex.com.br
ERP_FLEX_DEV_API_URL=https://sistema.erpflex.com.br/api/
ERP_FLEX_API_URL=https://sistema.erpflex.com.br/api/
ERP_FLEX_DEV_API_V2_URL=https://sistema.erpflex.com.br/api_v2/
ERP_FLEX_API_V2_URL=https://sistema.erpflex.com.br/api_v2/
ERP_FLEX_API_USERNAME=
ERP_FLEX_API_PASSWORD=

# IBGE
IBGE_CNAE_API_V2_URL=https://servicodados.ibge.gov.br/api/v2/cnae/

# SOC
SOC_API_URL=https://ws1.soc.com.br/WebSoc/exportadados
SOC_WS_URL=https://ws1.soc.com.br/WSSoc/

# ZapSign
ZAP_SIGN_API_URL=https://api.zapsign.com.br
