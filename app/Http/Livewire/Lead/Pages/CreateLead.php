<?php

namespace App\Http\Livewire\Lead\Pages;

use App\Actions\City\GetCityByName;
use App\Actions\Cnae\Queries\GetCnaeByCode;
use App\Actions\Procedure\GetProcedureDataForCrm;
use App\Integrations\Receita\Services\ReceitaService;
use App\Models\City;
use App\Models\CompanyBranch;
use App\Models\LeadCompany;
use App\Models\Procedure;
use App\Models\State;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;
use Livewire\WithFileUploads;
use Throwable;

class CreateLead extends Component
{
    use WithFileUploads;

    public string $action;
    public array $companies = [];
    public array $procedures = [];
    public array $leadCompanyBranchesArray = [];
    public ?int $leadCompanyId = null;
    public int $totalEmployeeCount = 0;
    public string $resourceRoute;
    public string $tab;
    public bool $comesFromAnotherModule = false;
    public ?int $currentReplicateCompanyProcedureCompanyKey = null;
    public ?int $currentReplicateCompanyProcedureProcedureKey = null;
    public bool $addCompanyModalOpened = false;
    public bool $hasErrors = false;
    public mixed $branchesCsv = null;

    public ?LeadCompany $leadCompany = null;

    public function mount(
        string $resourceRoute,
        string $action,
        ?int $leadCompanyId = null,
        bool $comesFromAnotherModule = false
    ): void {
        $this->resourceRoute = $resourceRoute;
        $this->action = $action;
        $this->leadCompanyId = $leadCompanyId;
        $this->comesFromAnotherModule = $comesFromAnotherModule;

        if ($leadCompanyId) {
            $this->updatedLeadCompanyId($leadCompanyId);
        }
    }

    public function getCompanyInfo(int $index)
    {
        if ($this->companies[$index]['company_tax_id_number'] === '') {
            $this->companies[$index]['company_name'] = '';
            $this->companies[$index]['company_trading_name'] = '';
            $this->companies[$index]['address_zipcode'] = '';
            $this->companies[$index]['address_address'] = '';
            $this->companies[$index]['address_number'] = '';
            $this->companies[$index]['address_additional_info'] = '';
            $this->companies[$index]['address_district'] = '';
            $this->companies[$index]['address_city_id'] = '';
            $this->companies[$index]['address_city_name'] = '';
            $this->companies[$index]['address_state_id'] = '';
            $this->companies[$index]['address_state_abbreviation'] = '';
            $this->companies[$index]['cnae_id'] = '';
            return;
        }

        try {
            $companyDetails = (new ReceitaService())->getCompanyDetails(unmask_cnpj($this->companies[$index]['company_tax_id_number']));
        } catch (Throwable) {
            return;
        }

        /** @var \App\Models\City $city */
        $city = City::query()
            ->where('name', $companyDetails->municipio)
            ->first();

        /** @var \App\Models\Cnae $cnae */
        $cnae = GetCnaeByCode::run(get_numbers($companyDetails->atividade_principal[0]->code));

        $this->companies[$index]['company_name'] = $companyDetails->nome;
        $this->companies[$index]['company_trading_name'] = $companyDetails->fantasia;
        $this->companies[$index]['address_zipcode'] = unmask_zipcode($companyDetails->cep);
        $this->companies[$index]['address_address'] = $companyDetails->logradouro;
        $this->companies[$index]['address_number'] = $companyDetails->numero;
        $this->companies[$index]['address_additional_info'] = $companyDetails->complemento;
        $this->companies[$index]['address_district'] = $companyDetails->bairro;
        $this->companies[$index]['address_city_id'] = $city->id;
        $this->companies[$index]['address_city_name'] = $city->name;
        $this->companies[$index]['address_state_id'] = $city->state_id;
        $this->companies[$index]['address_state_abbreviation'] = $city->state->abbreviation;
        $this->companies[$index]['cnae_id'] = $cnae->id;
    }

    public function addProposalItem()
    {
        $this->procedures[] = [
            'lead_proposal_item_procedure_id' => '',
            'lead_proposal_item_procedure_name' => '',
            'lead_proposal_item_type' => '',
            'lead_proposal_item_quantity' => collect($this->companies)->sum(fn(array $company): float => $company['employee_count']),
            'lead_proposal_item_minimum_amount' => 0,
            'lead_proposal_item_unit_amount' => 0,
            'lead_proposal_item_discount_percentage' => 0,
            'lead_proposal_item_discount_amount' => 0,
            'lead_proposal_item_addition_percentage' => 0,
            'lead_proposal_item_addition_amount' => 0,
            'lead_proposal_item_total_amount' => 0,
        ];

        $this->emit('proposalItemAdded', [
            'count' => count($this->procedures) - 1,
        ]);
    }

    public function openAddCompanyModal(): void
    {
        $this->addCompanyModalOpened = true;
        $this->emit('addCompanyModalOpened');
    }

    public function addCompany(?string $branchName = null): void
    {
        if (is_null($branchName)) {
            $this->companies[] = [
                'id' => 0,
                'company_name' => '',
                'company_trading_name' => '',
                'company_tax_id_number' => '',
                'branch_name' => '',
                'employee_count' => 0,
                'address_zipcode' => '',
                'address_address' => '',
                'address_number' => '',
                'address_additional_info' => '',
                'address_district' => '',
                'address_city_id' => '',
                'address_city_name' => '',
                'address_state_id' => '',
                'address_state_abbreviation' => '',
                'cnae_id' => '',
                'head_office' => false,
                'procedures' => [],
            ];
        } else {
            /** @var \App\Models\Company $branch */
            $branch = $this->leadCompany->company->companyBranches()
                ->whereHas('branch', fn(Builder $query): Builder => $query->where('branch_name', $branchName))
                ->first()
                ->branch;

            $this->companies[] = [
                'id' => 0,
                'company_name' => $branch->name,
                'company_trading_name' => $branch->trading_name,
                'company_tax_id_number' => $branch->tax_id_number,
                'branch_name' => $branch->branch_name,
                'employee_count' => $branch->employee_count,
                'address_zipcode' => $branch->zipcode,
                'address_address' => $branch->address,
                'address_number' => $branch->number,
                'address_additional_info' => $branch->additional_info,
                'address_district' => $branch->district,
                'address_city_id' => City::query()
                    ->where('name', $branch->city)
                    ->first()
                    ?->id,
                'address_city_name' => $branch->city,
                'address_state_id' => State::query()
                    ->where('abbreviation', $branch->state)
                    ->first()
                    ?->id,
                'address_state_abbreviation' => $branch->state,
                'cnae_id' => $branch->cnae_id,
                'head_office' => false,
                'procedures' => [],
            ];
        }

        if (count($this->companies) === 1) {
            $this->tab = 'company-' . array_key_first($this->companies) . '-tab';
        }

        $this->closeAddCompanyModal();
    }

    public function closeAddCompanyModal(): void
    {
        $this->addCompanyModalOpened = false;
        $this->emit('addCompanyModalClosed');
    }

    public function addCompanyProcedure(int $index): void
    {
        $this->companies[$index]['procedures'][] = [
            'lead_proposal_company_item_procedure_id' => '',
            'lead_proposal_company_item_procedure_name' => '',
            'lead_proposal_company_item_type' => '',
            'lead_proposal_company_item_quantity' => 1,
            'lead_proposal_company_item_unit_amount' => 0,
            'lead_proposal_company_item_discount_percentage' => 0,
            'lead_proposal_company_item_discount_amount' => 0,
            'lead_proposal_company_item_addition_percentage' => 0,
            'lead_proposal_company_item_addition_amount' => 0,
            'lead_proposal_company_item_total_amount' => 0,
        ];

        $this->emit('proposalCompanyItemAdded', [
            'index' => $index,
            'count' => count($this->companies[$index]['procedures']) - 1,
        ]);
    }

    public function deleteCompanyProcedure(int $index, int $procedureIndex): void
    {
        unset($this->companies[$index]['procedures'][$procedureIndex]);

        $this->emit('proposalCompanyItemDeleted', [
            'companyIndex' => $index,
            'procedures' => $this->companies[$index]['procedures']
        ]);
    }

    public function deleteProposalItem(int $procedureIndex): void
    {
        unset($this->procedures[$procedureIndex]);
        $this->emit('proposalItemDeleted', $this->procedures);
    }

    public function createLead()
    {
        $requestData = [
            'lead_company_id' => $this->leadCompanyId,
            'lead_items' => $this->procedures,
            'lead_branches' => $this->companies,
        ];

        /** @var \App\Models\Lead $lead */
        $lead = \App\Actions\Lead\CreateLead::run($requestData);

        return redirect_success('leads.edit', __('leads.responses.create.success'), $lead->id);
    }

    public function updatedCompanies(mixed $value, mixed $key)
    {
        $explodedKey = explode('.', $key);

        if ($explodedKey[1] === 'employee_count') {
            $this->totalEmployeeCount = collect($this->companies)->sum(fn(array $company): float => $company['employee_count']);

            foreach ($this->procedures as $key => $procedure) {
                $this->procedures[$key]['quantity'] = $this->totalEmployeeCount;
            }

            if (empty($this->companies)) {
                $this->hasErrors = false;
                return;
            }

            $this->hasErrors = !empty($this->companies)
                && collect($this->companies)->filter(fn(array $company): bool => (int) $company['employee_count'] === 0)->count() > 0;

            $this->emit('updateProceduresEmployeeCount', collect($this->companies)->sum(fn(array $company): float => $company['employee_count']));
        }
    }

    public function updatedLeadCompanyId(mixed $value): void
    {
        $this->leadCompany = LeadCompany::find($value);

        if (!$this->leadCompany || !$this->leadCompany->company) {
            $this->companies = [];

            if (!$this->leadCompany) {
                $this->hasErrors = true;
            }

            return;
        }

        $this->companies = array_merge(
            [[
                'company_name' => $this->leadCompany->company->name,
                'company_trading_name' => $this->leadCompany->company->trading_name,
                'company_tax_id_number' => $this->leadCompany->company->friendly_tax_id_number,
                'branch_name' => $this->leadCompany->company->branch_name,
                'address_zipcode' => $this->leadCompany->company->zipcode,
                'address_address' => $this->leadCompany->company->address,
                'address_number' => $this->leadCompany->company->number,
                'address_additional_info' => $this->leadCompany->company->additional_info,
                'address_district' => $this->leadCompany->company->district,
                'address_city_id' => City::query()
                    ->where('name', $this->leadCompany->company->city)
                    ->first()
                    ?->id,
                'address_city_name' => $this->leadCompany->company->city,
                'address_state_id' => State::query()
                    ->where('abbreviation', $this->leadCompany->company->state)
                    ->first()
                    ?->id,
                'address_state_abbreviation' => $this->leadCompany->company->state,
                'cnae_id' => $this->leadCompany->company->cnae_id,
                'employee_count' => $this->leadCompany->company->employee_count,
                'head_office' => $this->leadCompany->company->head_office,
                'procedures' => [],
            ]],
            $this->leadCompany->company->companyBranches
                ->filter(function (CompanyBranch $companyBranch): bool {
                    return !is_null($companyBranch->branch)
                        && $companyBranch->branch->technical_system === $this->leadCompany->company->technical_system;
                })
                ->map(fn(CompanyBranch $companyBranch): array => [
                    'company_name' => $companyBranch->branch->name,
                    'company_trading_name' => $companyBranch->branch->trading_name,
                    'company_tax_id_number' => $companyBranch->branch->friendly_tax_id_number,
                    'branch_name' => $companyBranch->branch->branch_name,
                    'address_zipcode' => $companyBranch->branch->zipcode,
                    'address_address' => $companyBranch->branch->address,
                    'address_number' => $companyBranch->branch->number,
                    'address_additional_info' => $companyBranch->branch->additional_info,
                    'address_district' => $companyBranch->branch->district,
                    'address_city_id' => City::query()
                        ->where('name', $companyBranch->branch->city)
                        ->first()
                        ?->id,
                    'address_city_name' => $companyBranch->branch->city,
                    'address_state_id' => State::query()
                        ->where('abbreviation', $companyBranch->branch->state)
                        ->first()
                        ?->id,
                    'address_state_abbreviation' => $companyBranch->branch->state,
                    'cnae_id' => $companyBranch->branch->cnae_id,
                    'employee_count' => $companyBranch->branch->employee_count,
                    'head_office' => $companyBranch->branch->head_office,
                    'procedures' => [],
                ])
                ->toArray()
        );

        $this->leadCompanyBranchesArray = array_merge(
            array_search($this->leadCompany->company->branch_name, array_column($this->companies, 'branch_name')) !== false
                ? [$this->leadCompany->company->id => $this->leadCompany->company->branch_name]
                : [],
            $this->leadCompany->company->companyBranches
                ->filter(function (CompanyBranch $companyBranch): bool {
                    return !is_null($companyBranch->branch)
                        && $companyBranch->branch->technical_system === $this->leadCompany->company->technical_system
                        && !in_array($companyBranch->branch->branch_name, array_column($this->companies, 'branch_name'));
                })
                ->mapWithKeys(fn(CompanyBranch $companyBranch): array => [$companyBranch->branch->branch_name => $companyBranch->branch->branch_name])
                ->toArray()
        );

        $this->totalEmployeeCount = collect($this->companies)->sum(fn(array $company): float => $company['employee_count']);

        if (empty($this->companies)) {
            $this->hasErrors = false;
            return;
        }

        $this->hasErrors = !empty($this->companies)
            && collect($this->companies)->filter(fn(array $company): bool => (int) $company['employee_count'] === 0)->count() > 0;

        $this->tab = 'company-' . array_key_first($this->companies) . '-tab';
    }

    public function openReplicateCompanyProcedureModal(int $companyIndex, int $procedureIndex): void
    {
        $this->currentReplicateCompanyProcedureCompanyKey = $companyIndex;
        $this->currentReplicateCompanyProcedureProcedureKey = $procedureIndex;
        $this->emit('openReplicateCompanyProcedureModal');
    }

    public function replicateProcedureToUnits(): void
    {
        foreach ($this->companies as $key => $company) {
            if ((int) $key === (int) $this->currentReplicateCompanyProcedureCompanyKey) {
                continue;
            }

            /** @var array $crmProcedureData */
            $crmProcedureData = GetProcedureDataForCrm::run(
                Procedure::find($this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_id']),
                $this->companies[$key]['address_city_id'],
                $this->companies[$key]['employee_count'],
            );

            $this->companies[$key]['procedures'][] = [
                'lead_proposal_company_item_procedure_id' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_id'],
                'lead_proposal_company_item_procedure_name' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_name'],
                'lead_proposal_company_item_type' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_type'],
                'lead_proposal_company_item_quantity' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_quantity'],
                'lead_proposal_company_item_unit_amount' => $crmProcedureData['amount'],
                'lead_proposal_company_item_discount_percentage' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_discount_percentage'],
                'lead_proposal_company_item_discount_amount' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_discount_amount'],
                'lead_proposal_company_item_addition_percentage' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_addition_percentage'],
                'lead_proposal_company_item_addition_amount' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_addition_amount'],
                'lead_proposal_company_item_total_amount' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_quantity'] * ($crmProcedureData['amount'] - $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_discount_amount'] + $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_addition_amount']),
            ];
        }

        $this->closeReplicateCompanyProcedureModal();
    }

    public function closeReplicateCompanyProcedureModal(): void
    {
        $this->currentReplicateCompanyProcedureCompanyKey = null;
        $this->currentReplicateCompanyProcedureProcedureKey = null;
    }

    public function removeCompany(int $index): void
    {
        unset($this->companies[$index]);
        $this->tab = 'company-' . array_key_first($this->companies) . '-tab';

        if (empty($this->companies)) {
            $this->hasErrors = false;
            return;
        }

        $this->hasErrors = !empty($this->companies)
            && collect($this->companies)->filter(fn(array $company): bool => (int) $company['employee_count'] === 0)->count() > 0;
    }

    public function removeInvalidBranches(): void
    {
        foreach ($this->companies as $key => $company) {
            if ((int)$company['employee_count'] > 0) {
                continue;
            }

            $this->removeCompany($key);
        }
    }

    public function processCsv(): void
    {
        if (!$this->branchesCsv) {
            $this->emit('showCsvErrorModal', 'Anexe um arquivo para poder importar filiais!');
            return;
        }

        $open = fopen($this->branchesCsv->getRealPath(), 'r');
        $data = fgetcsv($open, 1000, ";");

        $branches = [];
        $hasCsvError = false;

        while (($data = fgetcsv($open, 1000, ",")) !== FALSE) {
            $cells = explode(';', $data[0]);

            $hasLineError = is_null($cells[0]) || trim($cells[0]) === ''
                || is_null($cells[1]) || trim($cells[1]) === ''
                || is_null($cells[2]) || trim($cells[2]) === ''
                || is_null($cells[3]) || trim($cells[3]) === '';

            if ($hasLineError) {
                $hasCsvError = true;
                break;
            }

            $branches[] = $cells;
        }

        if ($hasCsvError) {
            $this->emit('showCsvErrorModal', 'Existem linhas do arquivo CSV com informações obrigatórias faltantes (primeiras cinco colunas).');
            return;
        }

        foreach ($branches as $branch) {
            /** @var \App\Models\City|null $city */
            $city = GetCityByName::run(trim($branch[2]));

            if (!$city) {
                $hasCsvError = true;
                break;
            }

            $this->companies[] = [
                'company_tax_id_number' => $branch[4],
                'branch_name' => $branch[0],
                'employee_count' => $branch[1],
                'address_zipcode' => isset($branch[5]) && !is_null($branch[5]) && trim($branch[5]) !== ''
                    ? str_pad($branch[5], 8, '0', STR_PAD_LEFT)
                    : null,
                'address_address' => isset($branch[6]) && !is_null($branch[6]) && trim($branch[6]) !== ''
                    ? $branch[6]
                    : null,
                'address_number' => isset($branch[7]) && !is_null($branch[7]) && trim($branch[7]) !== ''
                    ? $branch[7]
                    : null,
                'address_additional_info' => isset($branch[8]) && !is_null($branch[8]) && trim($branch[8]) !== ''
                    ? $branch[8]
                    : null,
                'address_district' => isset($branch[9]) && !is_null($branch[9]) && trim($branch[9]) !== ''
                    ? $branch[9]
                    : null,
                'address_city_id' => $city->id,
                'address_city_name' => $city->name,
                'address_state_id' => $city->state_id,
                'address_state_abbreviation' => $city->state->abbreviation,
                'head_office' => false,
                'procedures' => [],
            ];
        }

        if ($hasCsvError) {
            $this->emit('showCsvErrorModal', 'Existem linhas do arquivo CSV com cidades não encontradas.');
            return;
        }

        $this->totalEmployeeCount = collect($this->companies)->sum(fn(array $company): float => $company['employee_count']);

        $this->tab = 'company-' . array_key_first($this->companies) . '-tab';
    }
}
