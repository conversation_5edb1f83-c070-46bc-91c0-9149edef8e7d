<?php

namespace App\Imports;

use App\Actions\Company\Queries\GetCompanyBySocCompanyEnvironmentSocIdAndBranchName;
use App\Actions\CompanyEmployee\Queries\GetCompanyEmployeeByCompanyIdTaxIdNumberAndEmployeeSocId;
use App\Actions\Employee\Queries\GetEmployeeByTaxIdNumber;
use App\Models\CompanyEmployee;
use App\Models\Employee;
use App\Support\Helpers\ExcelHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class EmployeeInactivityImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private array $errors;

    public function __construct(
        private int $userId,
        private int $importId
    ) {}

    public function headingRow(): int
    {
        return 2;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function collection(Collection $collection)
    {
        $socIds = $collection
            ->pluck('codigo_empresa')
            ->unique()
            ->toArray();

        CompanyEmployee::query()
            ->whereRelation('company', function (Builder $query) use ($socIds): Builder {
                return $query->whereRelation('socCompanyEnvironment', function (Builder $query) use ($socIds): Builder {
                    return $query->whereIn('soc_id', array_values($socIds));
                });
            })
            ->whereDate('updated_at', '<', now()->format('Y-m-d'))
            ->update(['found_in_batch_update' => false]);

        foreach ($collection as $row) {
            if (is_null($row['cpf'])) {
                $this->errors[] = array_merge(['error' => 'CPF nulo.'], $row->toArray());
                continue;
            }

            /** @var \App\Models\Company|null $company */
            $company = GetCompanyBySocCompanyEnvironmentSocIdAndBranchName::run($row['codigo_empresa'], $row['unidade']);

            if (is_null($company)) {
                $this->errors[] = array_merge(['error' => 'A unidade não foi encontrada.'], $row->toArray());
                continue;
            }

            /** @var \App\Models\Employee|null $employee */
            $employee = GetEmployeeByTaxIdNumber::run($row['cpf']);

            if (is_null($employee)) {
                /** @var \App\Models\Employee $employee */
                $employee = Employee::create([
                    'name' => $row['nome'],
                    'tax_id_number' => $row['cpf'],
                ]);
            }

            /** @var \App\Models\CompanyEmployee|null $companyEmployee */
            $companyEmployee = GetCompanyEmployeeByCompanyIdTaxIdNumberAndEmployeeSocId::run($company->id, $row['cpf'], $row['codigo']);

            if (is_null($companyEmployee)) {
                CompanyEmployee::create([
                    'company_id' => $company->id,
                    'employee_id' => $employee->id,
                    'employee_soc_id' => $row['codigo'],
                    'department_name' => $row['setor'],
                    'job_title_name' => $row['cargo'],
                    'corporate_registry_no' => $row['matricula'] ?? null,
                    'found_in_batch_update' => true,
                    'admitted_at' => !is_null($row['dtadmissao']) && $row['dtadmissao'] !== ''
                        ? ExcelHelper::transformDate($row['dtadmissao'])
                        : null,
                    'resigned_at' => trim(mb_strtoupper($row['situacao'])) === 'INATIVO'
                        ? Carbon::createFromFormat('d/m/Y', $row['data_de_inativacao'])
                        : null,
                ]);

                continue;
            }

            $companyEmployee->update([
                'department_name' => $row['setor'],
                'job_title_name' => $row['cargo'],
                'corporate_registry_no' => $row['matricula'] ?? null,
                'found_in_batch_update' => true,
                'admitted_at' => !is_null($row['dtadmissao']) && $row['dtadmissao'] !== ''
                    ? ExcelHelper::transformDate($row['dtadmissao'])
                    : null,
                'resigned_at' => trim(mb_strtoupper($row['situacao'])) === 'INATIVO'
                    ? Carbon::createFromFormat('d/m/Y', $row['data_de_inativacao'])
                    : null,
            ]);
        }
    }
}
