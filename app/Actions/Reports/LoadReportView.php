<?php

namespace App\Actions\Reports;

use App\Support\Dictionaries\ReportDictionary;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class LoadReportView
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return mixed
     */
    public function handle(ActionRequest $request): mixed
    {
        switch ($request->input('report_name')) {
            case ReportDictionary::COMMISSION_PAYMENT:
                return view('app.reports.billing.index_commission_payment_report');
            case ReportDictionary::COMPANIES:
                return view('app.reports.management.index_companies_report');
            case ReportDictionary::COMPANIES_WITH_LIFE_COUNT:
                return view('app.reports.management.index_companies_with_life_count_report');
            case ReportDictionary::COMPANY_LOSS_RATIO:
                return view('app.reports.billing.index_company_loss_ratio_report');
            case ReportDictionary::PROVIDERS:
                return view('app.reports.management.index_providers_report');
            case ReportDictionary::SUPPLIERS:
                return view('app.reports.management.index_suppliers_report');
            case ReportDictionary::PROVIDER_LOSS_RATIO:
                return view('app.reports.billing.index_provider_loss_ratio_report');
            case ReportDictionary::PROVIDER_PROCEDURES:
                return view('app.reports.management.index_provider_procedures_report');
            case ReportDictionary::CONTRACTS:
                return view('app.reports.contract.index_contracts_report');
            case ReportDictionary::CONTRACTS_WITHOUT_SERVICE_ORDERS:
                return view('app.reports.contract.index_contracts_without_service_orders_report');
            case ReportDictionary::PENDING_BILLING_CONTRACTS_FOR_PERIOD:
                return view('app.reports.billing.index_pending_billing_contracts_for_period_report');
            case ReportDictionary::SERVICE_ORDERS:
                return view('app.reports.engineering.index_service_orders_report');
            case ReportDictionary::OPEN_RECEIVABLES:
                return view('app.reports.finance.index_open_receivables_report');
            case ReportDictionary::PROVIDER_EXPENSES:
                return view('app.reports.entries.index_provider_expenses_report');
            case ReportDictionary::SUPPLIER_EXPENSES:
                return view('app.reports.entries.index_supplier_expenses_report');
            case ReportDictionary::PROVIDER_COMPANIES:
                return view('app.reports.management.index_provider_companies_report');
        }
    }
}
