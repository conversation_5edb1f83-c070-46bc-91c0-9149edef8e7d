<?php

namespace App\Actions\Imports;

use App\Core\Http\Requests\ImportActionRequest;
use App\Core\Imports\BaseImport;
use App\Core\Imports\Interfaces\Importable;
use App\Imports\EmployeeInactivityImport;
use App\Models\Import;
use App\Support\Dictionaries\ImportDictionary;
use App\Support\Helpers\DigitalOcean\DigitalOceanExcelHelper;
use Illuminate\Support\Facades\Storage;
use Lorisleiva\Actions\Concerns\AsAction;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;

class ImportEmployeeInactivityData extends BaseImport implements Importable
{
    use AsAction;

    public array $rules = [
        'file' => 'required',
    ];

    public function asController(ImportActionRequest $request): mixed
    {
        if ($request->comesFromGetMethod()) {
            return LoadImportView::run(ImportDictionary::EMPLOYEE_INACTIVITY);
        }

        try {
            $this->handle($request->file('file'));
            return redirect_success('home', __('general.responses.import.success'));
        } catch (Throwable) {
            return redirect_import_error();
        }
    }

    public function handle(mixed $file): void
    {
        $import = (new Import())
            ->type(Import::TYPE_EMPLOYEE_INACTIVITY)
            ->withFile($file);

        $import->save();

        $dateTime = date('YmdHis');

        $file->storeAs($import->path, "employee_inactivity_$dateTime.xlsx");

        try {
            $userId = auth()->id();

            dispatch(function () use ($import, $userId, $dateTime) {
                $import->update([
                    'last_processed_at' => now(),
                    'attempt_no' => $import->attempt_no + 1,
                ]);

                Excel::import(
                    new EmployeeInactivityImport($userId, $import->id),
                    storage_path("app/imports/employee-inactivity/employee_inactivity_$dateTime.xlsx")
                );

                $import->update(['imported' => true]);

                // User::findOrFail($userId)->notify(
                //     new ImportCompaniesJobFinishedNotification()
                // );
            })->onQueue(config('queue.default_queue_names.imports'));
        } catch (Throwable $th) {
            throw_error($th);
        } finally {
            Storage::delete(storage_path("app/imports/employee-inactivity/employee_inactivity_$dateTime.xlsx"));
        }
    }
}
