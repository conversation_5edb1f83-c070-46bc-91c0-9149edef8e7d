<?php

namespace App\Actions\Imports;

use App\Support\Dictionaries\ImportDictionary;
use Lorisleiva\Actions\Concerns\AsAction;

class GetImports
{
    use AsAction;

    /**
     * The imports array.
     *
     * @var array
     */
    protected $imports = [
        'management' => [
            'title' => 'Cadastros',
            'items' => [
                ImportDictionary::EMPLOYEE_INACTIVITY => 'Quadro de funcionários'
            ]
        ]
    ];

    /**
     * Handle the action.
     *
     * @return mixed
     */
    public function handle(): mixed
    {
        $availableReports = [];

        foreach ($this->imports as $importGroupName => $importGroup) {
            foreach ($importGroup['items'] as $importNameKey => $importNameValue) {
                if (!auth()->user()->can("create_{$importNameKey}_import")) {
                    continue;
                }

                $availableReports[$importGroup['title']][$importNameKey] = $importNameValue;
            }
        }

        return view('app.imports.index_imports', [
            'availableReports' => $availableReports,
            'resourceRoute' => 'imports'
        ]);
    }
}
