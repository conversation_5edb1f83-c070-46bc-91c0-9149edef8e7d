<?php

namespace App\Actions\Company\Queries;

use App\Models\Company;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompanyBySocCompanyEnvironmentSocIdAndBranchName
{
    use AsAction;

    public function handle(int $socCompanyEnvironmentId, string $branchName): ?Company
    {
        return Company::query()
            ->where('branch_name', trim($branchName))
            ->whereRelation('socCompanyEnvironment', 'soc_id', $socCompanyEnvironmentId)
            ->first();
    }
}
