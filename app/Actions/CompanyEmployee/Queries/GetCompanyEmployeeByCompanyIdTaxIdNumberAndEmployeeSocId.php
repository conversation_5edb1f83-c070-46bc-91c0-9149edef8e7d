<?php

namespace App\Actions\CompanyEmployee\Queries;

use App\Models\CompanyEmployee;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCompanyEmployeeByCompanyIdTaxIdNumberAndEmployeeSocId
{
    use AsAction;

    public function handle(int $companyId, string $taxIdNumber, string $employeeSocId): ?CompanyEmployee
    {
        return CompanyEmployee::query()
            ->where('company_id', $companyId)
            ->where('employee_soc_id', $employeeSocId)
            ->whereRelation('employee', 'tax_id_number', $taxIdNumber)
            ->first();
    }
}
