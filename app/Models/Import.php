<?php

namespace App\Models;

use App\Core\Module;
use Exception;

/**
 * Import model.
 *
 * @package App\Models
 *
 * @property  int $id  Import ID.
 * @property  string $type  Import type.
 * @property  string $path  Import file path.
 * @property  string $filename  Import file name.
 * @property  int $user_id  ID for the user who imported
 * @property  \Carbon\Carbon $last_processed_at  Timestamp of last processing.
 * @property  int $attempt_no  Last attempt number.
 * @property  bool $imported  Defines whether the file was imported or not.
 * @property  \Carbon\Carbon $created_at  Datetime when the model was created.
 * @property  \Carbon\Carbon $updated_at  Datetime when the model was updated.
 */
class Import extends Model
{
    public const CLASS_NAME = self::class;
    public const HTML_ENTITY = 'import';
    public const MODULE = Module::LOGS;
    public const RESOURCE_ROUTE = 'imports';

    public const TYPE_COMPANIES = 'companies';
    public const TYPE_SUPPLIERS = 'suppliers';
    public const TYPE_PROVIDERS = 'providers';
    public const TYPE_CONTRACTS = 'contracts';
    public const TYPE_CONTRACT_RECEIVABLES = 'contract-receivables';
    public const TYPE_PROVIDER_PROCEDURES = 'provider-procedures';
    public const TYPE_SOCNET_EXAMS = 'socnet-exams';
    public const TYPE_CNAES = 'cnaes';
    public const TYPE_EMPLOYEE_INACTIVITY = 'employee-inactivity';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'path',
        'filename',
        'last_processed_at',
        'attempt_no',
        'imported',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (Import $import) {
            $import->user_id = auth()->id();
        });
    }

    /**
     * Define the import's type.
     *
     * @param  string $type
     *
     * @return $this
     */
    public function type(string $type): static
    {
        return $this->fill(compact('type'));
    }

    /**
     * Define the import's file.
     *
     * @param  mixed $file
     *
     * @return $this
     * @throws \Exception
     */
    public function withFile(mixed $file): static
    {
        if (is_null($this->type)) {
            throw new Exception('type not set');
        }

        return $this->fill([
            'path' => "imports/$this->type/",
            'filename' => $file->getClientOriginalName(),
        ]);
    }

    /**
     * Get all imports for DataTable displaying.
     *
     * @return mixed
     */
    public static function getForDataTable(): mixed
    {
        return self::orderByDesc('id');
    }
}
