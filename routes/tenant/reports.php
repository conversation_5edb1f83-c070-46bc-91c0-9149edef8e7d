<?php

use App\Actions\CompanyClosing\GenerateCompanyClosingPdfReport;
use App\Actions\CompanyClosing\GenerateCompanyClosingExcelReport;
use App\Actions\Reports\GenerateCommissionPaymentReport;
use App\Actions\Reports\GenerateCompaniesReport;
use App\Actions\Reports\GenerateCompaniesWithLifeCountReport;
use App\Actions\Reports\GenerateCompanyLossRatioReport;
use App\Actions\Reports\GenerateContractsReport;
use App\Actions\Reports\GenerateContractsWithoutServiceOrdersReport;
use App\Actions\Reports\GenerateOpenReceivablesReport;
use App\Actions\Reports\GeneratePendingBillingContractsForPeriodReport;
use App\Actions\Reports\GenerateProviderCompaniesReport;
use App\Actions\Reports\GenerateProviderExpensesReport;
use App\Actions\Reports\GenerateProviderLossRatioReport;
use App\Actions\Reports\GenerateProvidersReport;
use App\Actions\Reports\GenerateProviderProceduresReport;
use App\Actions\Reports\GenerateServiceOrdersReport;
use App\Actions\Reports\GenerateSupplierExpensesReport;
use App\Actions\Reports\GenerateSuppliersReport;
use App\Actions\Reports\GetGroupedExpenses;
use App\Actions\Reports\GetReports;
use App\Actions\Reports\LoadReportView;

Route::get('', GetReports::class)->name('reports.index');
Route::post('load-report-view', LoadReportView::class)->name('reports.load_report_view');

Route::prefix('management')->group(function () {
    Route::prefix('companies')
        ->name('reports.companies')
        ->group(function () {
            Route::get('', GenerateCompaniesReport::class);
            Route::post('', GenerateCompaniesReport::class);
        });

    Route::prefix('companies-with-life-count')
        ->name('reports.companies_with_life_count')
        ->group(function () {
            Route::get('', GenerateCompaniesWithLifeCountReport::class);
            Route::post('', GenerateCompaniesWithLifeCountReport::class);
        });

    Route::prefix('providers')
        ->name('reports.providers')
        ->group(function () {
            Route::get('', GenerateProvidersReport::class);
            Route::post('', GenerateProvidersReport::class);
        });

    Route::prefix('provider-procedures')
        ->name('reports.provider_procedures')
        ->group(function () {
            Route::get('', GenerateProviderProceduresReport::class);
            Route::post('', GenerateProviderProceduresReport::class);
        });

        Route::prefix('provider-companies')
        ->name('reports.provider_companies')
        ->group(function () {
            Route::get('', GenerateProviderCompaniesReport::class);
            Route::post('', GenerateProviderCompaniesReport::class);
        });

    Route::prefix('suppliers')
        ->name('reports.suppliers')
        ->group(function () {
            Route::get('', GenerateSuppliersReport::class);
            Route::post('', GenerateSuppliersReport::class);
        });
});

Route::prefix('contracts')->group(function () {
    Route::prefix('contracts')
        ->name('reports.contracts')
        ->group(function () {
            Route::get('', GenerateContractsReport::class);
            Route::post('', GenerateContractsReport::class);
        });

    Route::prefix('contracts-without-service-orders')
        ->name('reports.contracts_without_service_orders')
        ->group(function () {
            Route::get('', GenerateContractsWithoutServiceOrdersReport::class);
            Route::post('', GenerateContractsWithoutServiceOrdersReport::class);
        });
});

Route::prefix('operation')->group(function () {
    Route::prefix('service-orders')
        ->name('reports.service_orders')
        ->group(function () {
            Route::get('', GenerateServiceOrdersReport::class);
            Route::post('', GenerateServiceOrdersReport::class);
        });
});

Route::prefix('entries')->group(function () {
    Route::prefix('provider-expenses')
        ->name('reports.provider_expenses')
        ->group(function () {
            Route::get('', GenerateProviderExpensesReport::class);
            Route::post('', GenerateProviderExpensesReport::class);
        });

    Route::prefix('supplier-expenses')
        ->name('reports.supplier_expenses')
        ->group(function () {
            Route::get('', GenerateSupplierExpensesReport::class);
            Route::post('', GenerateSupplierExpensesReport::class);
        });
});

Route::prefix('billing')->group(function () {
    Route::prefix('pending-billing-contracts-for-period')
        ->name('reports.pending_billing_contracts_for_period')
        ->group(function () {
            Route::get('', GeneratePendingBillingContractsForPeriodReport::class);
            Route::post('', GeneratePendingBillingContractsForPeriodReport::class);
        });

    Route::prefix('commission_payment')
        ->name('reports.commission_payment')
        ->group(function () {
            Route::get('', GenerateCommissionPaymentReport::class);
            Route::post('', GenerateCommissionPaymentReport::class);
        });

    Route::prefix('company_loss_ratio')
        ->name('reports.company_loss_ratio')
        ->group(function () {
            Route::get('', GenerateCompanyLossRatioReport::class);
            Route::post('', GenerateCompanyLossRatioReport::class);
        });

        Route::prefix('provider_loss_ratio')
        ->name('reports.provider_loss_ratio')
        ->group(function () {
            Route::get('', GenerateProviderLossRatioReport::class);
            Route::post('', GenerateProviderLossRatioReport::class);
        });
});

Route::prefix('finance')->group(function () {
    Route::prefix('open_receivables')
        ->name('reports.open_receivables')
        ->group(function () {
            Route::get('', GenerateOpenReceivablesReport::class);
            Route::post('', GenerateOpenReceivablesReport::class);
        });
});

Route::get('get-grouped-expenses', GetGroupedExpenses::class)->name('reports.get_grouped_expenses');
Route::get('pdf/{contract}/{referringMonth}/{referringYear}', GenerateCompanyClosingPdfReport::class)->name('reports.pdf');
Route::get('excel/{contract}/{referringMonth}/{referringYear}', GenerateCompanyClosingExcelReport::class)->name('reports.excel');
