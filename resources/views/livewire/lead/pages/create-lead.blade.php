<div>
    <x-form.create-form :$resourceRoute wire:submit.prevent="createLead" :saveButtonRightMargin="true" :disabledSaveButton="$hasErrors">
        <x-card :title="__('leads.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('crm_funnels.panel')">
            <x-form.section>
                <x-form.row>
                    <x-input.select md="6" lg="6" :$resourceRoute :$action field="lead_company_id" required="true" :wireIgnore="true">
                        @if ($comesFromAnotherModule && $leadCompanyId)
                            <option value="{{ $leadCompanyId }}">{{ $leadCompany->name }}</option>
                        @endif
                    </x-input.select>
                    <x-input.text md="3" lg="3" :$resourceRoute :$action field="lead_company_trading_name" disabled="true" :value="$leadCompany?->trading_name" />
                    <x-input.text md="3" lg="3" :$resourceRoute :$action field="lead_company_tax_id_number" disabled="true" :value="$leadCompany?->friendly_tax_id_number" />
                    <input type="hidden" name="total_employee_count" id="total_employee_count" wire:model="totalEmployeeCount">
                </x-form.row>
                <x-form.row :marginTop="true">
                    <div class="col-sm-12 col-md-3 col-lg-3">
                        <label for="branches_csv" class="mb-1">Planilha de unidades</label>
                        <input type="file" id="branches_csv" name="branches_csv" class="form-control" accept=".csv" wire:model="branchesCsv">
                    </div>
                    <div class="col-sm-12 col-md-2 col-lg-2">
                        <label for="branches_csv" class="mb-1">&nbsp;</label>
                        <button type="button" class="btn btn-outline-primary shadow-sm ml-2 form-control" wire:click="processCsv">
                            <i class="fa fa-upload mr-1"></i> Importar filiais
                        </button>
                    </div>
                </x-form.row>
            </x-form.section>
            <x-form.section title="Serviços" :inlineCreateButton="true" :marginTop="true">
                <x-slot name="inlineCreateButtonSlot">
                    <button type="button" class="btn btn-outline-primary btn-sm shadow-sm" wire:click="addProposalItem()">
                        <i class="fa fa-plus mr-1"></i> Adicionar serviço
                    </button>
                </x-slot>
                <x-form.row>
                    <div class="col-12">
                        <table width="100%" class="table-striped" style="margin-top: 2px; border: 1px solid lightgrey;">
                            <thead>
                                <th width="40%">Serviço</th>
                                <th width="7%">Valor mínimo</th>
                                <th width="7%">Valor unit.</th>
                                <th width="7%">% desc.</th>
                                <th width="7%">R$ desc.</th>
                                <th width="7%">% acrésc.</th>
                                <th width="7%">R$ acrésc.</th>
                                <th width="11%">Valor total</th>
                                <th width="7%">Ações</th>
                            </thead>
                            <tbody>
                                @foreach ($procedures as $procedureKey => $procedure)
                                    <tr>
                                        <td width="40%" wire:ignore>
                                            <script>
                                                initializeSelect2("lead_proposal_item_procedure_id_{{ $procedureKey }}", "{{ route('procedures.get_active_by_name') }}", 'Digite o nome de um serviço');
                                            </script>
                                            <select id="lead_proposal_item_procedure_id_{{ $procedureKey }}" name="lead_proposal_item_procedure[{{ $procedureKey }}]" class="form-control">
                                                <option value="{{ $procedure['lead_proposal_item_procedure_id'] }}">{{ $procedure['lead_proposal_item_procedure_name'] }}</option>
                                            </select>
                                            <input id="lead_proposal_item_type_{{ $procedureKey }}" name="lead_proposal_item_type[{{ $procedureKey }}]" type="hidden" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_minimum_amount_{{ $procedureKey }}" name="lead_proposal_item_minimum_amount[{{ $procedureKey }}]" class="form-control amount-input" data-procedure-key="{{ $procedureKey }}" value="{{ $procedure['lead_proposal_item_minimum_amount'] }}" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_unit_amount_{{ $procedureKey }}" name="lead_proposal_item_unit_amount[{{ $procedureKey }}]" class="form-control" readonly="true" value="{{ format_money($procedure['lead_proposal_item_unit_amount']) }}" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_discount_percentage_{{ $procedureKey }}" name="lead_proposal_item_discount_percentage[{{ $procedureKey }}]" class="form-control percentage-input" value="{{ format_percentage($procedure['lead_proposal_item_discount_percentage']) }}" disabled="true" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_discount_amount_{{ $procedureKey }}" name="lead_proposal_item_discount_amount[{{ $procedureKey }}]" class="form-control amount-input" value="{{ format_money($procedure['lead_proposal_item_discount_amount']) }}" disabled="true" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_addition_percentage_{{ $procedureKey }}" name="lead_proposal_item_addition_percentage[{{ $procedureKey }}]" class="form-control percentage-input" value="{{ format_percentage($procedure['lead_proposal_item_addition_percentage']) }}" disabled="true" />
                                        </td>
                                        <td width="7%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_addition_amount_{{ $procedureKey }}" name="lead_proposal_item_addition_amount[{{ $procedureKey }}]" class="form-control amount-input" value="{{ format_money($procedure['lead_proposal_item_addition_amount']) }}" disabled="true" />
                                        </td>
                                        <td width="11%" wire:ignore>
                                            <input type="text" id="lead_proposal_item_total_amount_{{ $procedureKey }}" name="lead_proposal_item_total_amount[{{ $procedureKey }}]" class="form-control amount-input" readonly="true" value="{{ format_money($procedure['lead_proposal_item_total_amount']) }}" />
                                        </td>
                                        <td width="7%">
                                            <button class="btn btn-outline-secondary btn-sm shadow-sm" type="button" id="lead_proposal_item_apply_discount_{{ $procedureKey }}">
                                                <i class="fa fa-minus" aria-hidden="true"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm shadow-sm" type="button" id="lead_proposal_item_apply_addition_{{ $procedureKey }}">
                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm shadow-sm" type="button" wire:click="deleteProposalItem({{ $procedureKey }})">
                                                <i class="fa fa-trash" aria-hidden="true"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </x-form.row>
            </x-form.section>
        </x-card>

        <div class="mx-4">
            <hr>
        </div>

        <x-card title="Unidades" :marginTop="true">
            <x-form.row>
                <div class="col align-self-start">
                    <button type="button" class="btn btn-outline-primary btn-sm shadow-sm" wire:click="openAddCompanyModal">
                        <i class="fa fa-plus mr-1"></i> Adicionar unidade
                    </button>
                </div>
                <div class="col align-self-end text-right">
                    <button type="button" class="btn btn-outline-danger btn-sm shadow-sm" data-toggle="modal" data-target="#remove-invalid-branches-modal">
                        <i class="fa fa-ban mr-1"></i> Desconsiderar filiais inválidas
                    </button>
                </div>
            </x-form.row>
            @if (count($companies) > 0)
                <div class="mt-4">
                    <x-tabs id="lead-create">
                        <x-slot name="tabHeaders">
                            @foreach ($companies as $key => $company)
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link @if ($tab === "company-$key-tab") active @endif" role="tab" type="button" wire:click="$set('tab', 'company-{{ $key }}-tab')">
                                        <span @if ((int) $company['employee_count'] === 0) style="color: red" @endif>
                                            @if ((bool) $company['head_office'])<strong>@endif
                                                {{ (is_null($company['branch_name']) || $company['branch_name'] === '') ? ('Filial ' . $key) : $company['branch_name'] }}
                                            @if ((bool) $company['head_office'])</strong>@endif
                                        </span>
                                    </button>
                                </li>
                            @endforeach
                        </x-slot>
                        <x-slot name="tabContent">
                            @foreach ($companies as $key => $company)
                                @if ($tab === "company-$key-tab")
                                    <x-tabs.tab-content-item id="company-{{ $key }}-tab" :active="true">
                                        <x-form.section title="Geral" :marginTop="true" :inlineCreateButton="true">
                                            <x-slot name="inlineCreateButtonSlot">
                                                <button type="button" class="btn btn-outline-danger btn-sm shadow-sm" wire:click="removeCompany({{ $key }})">
                                                    <i class="fa fa-minus mr-1"></i> Remover unidade
                                                </button>
                                            </x-slot>
                                            <x-form.row>
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" id="tax_id_number_{{ $key }}" field="tax_id_number[{{ $key }}]" :label="__('leads.forms.fields.tax_id_number')" wire:change.lazy="getCompanyInfo({{ $key }})" wire:model.lazy="companies.{{ $key }}.company_tax_id_number" />
                                            </x-form.row>
                                            <x-form.row :marginTop="true">
                                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="name[{{ $key }}]" id="name_{{ $key }}" required="true" :label="__('leads.forms.fields.name')" wire:model="companies.{{ $key }}.company_name" />
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="trading_name[{{ $key }}]" :label="__('leads.forms.fields.trading_name')" wire:model="companies.{{ $key }}.company_trading_name" />
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="branch_name[{{ $key }}]" :label="__('leads.forms.fields.branch_name')" wire:model="companies.{{ $key }}.branch_name" required="true" />
                                            </x-form.row>
                                            <x-form.row :marginTop="true">
                                                <div class="col-sm-12 col-md-3 col-lg-3">
                                                    <div class="d-flex justify-content-between">
                                                        <label for="state_registration_no" class="mb-1">Inscrição estadual</label>
                                                        <div>
                                                            <input type="checkbox" name="exempt_from_state_registration[{{ $key }}]" id="exempt_from_state_registration[{{ $key }}]"> Isento?
                                                        </div>
                                                    </div>
                                                    <input type="text" id="state_registration_no[{{ $key }}]" name="state_registration_no[{{ $key }}]" class="form-control" />
                                                </div>
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="city_registration_no[{{ $key }}]" :label="__('leads.forms.fields.city_registration_no')" />
                                                <x-input.number md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="employee_count[{{ $key }}]" id="employee_count_{{ $key }}" :label="__('leads.forms.fields.employee_count')" wire:model.lazy="companies.{{ $key }}.employee_count" />
                                            </x-form.row>
                                            <x-form.row :marginTop="true">
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="zipcode[{{ $key }}]" :label="__('leads.forms.fields.zipcode')" id="zipcode_{{ $key }}" wire:model="companies.{{ $key }}.address_zipcode" />
                                            </x-form.row>
                                            <x-form.row :marginTop="true">
                                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="create" field="address[{{ $key }}]" :label="__('leads.forms.fields.address')" id="address_{{ $key }}" wire:model="companies.{{ $key }}.address_address" />
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="number[{{ $key }}]" :label="__('leads.forms.fields.number')" id="number_{{ $key }}" wire:model="companies.{{ $key }}.address_number" />
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="additional_info[{{ $key }}]" :label="__('leads.forms.fields.additional_info')" id="additional_info_{{ $key }}" wire:model="companies.{{ $key }}.address_additional_info" />
                                            </x-form.row>
                                            <x-form.row :marginTop="true">
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="district[{{ $key }}]" :label="__('leads.forms.fields.district')" id="district_{{ $key }}" wire:model="companies.{{ $key }}.address_district" />
                                                <div class="col-sm-12 col-md-3 col-lg-3" wire:ignore>
                                                    <x-input.label :$resourceRoute :$action field="city" />
                                                    <select id="city_{{ $key }}" name="city[{{ $key }}]" class="form-control">
                                                        <option value="{{ $company['address_city_id'] }}">{{ $company['address_city_name'] }}</option>
                                                    </select>
                                                    <script>
                                                        $('#city_{{ $key }}').select2({
                                                            theme: 'bootstrap4',
                                                            language: {
                                                                inputTooShort: function () {
                                                                    return 'Digite um ou mais caracteres';
                                                                }
                                                            },
                                                            ajax: {
                                                                url: "{{ route('cities.get_by_name') }}",
                                                                dataType: 'json',
                                                                delay: 250,
                                                                data: function (params) {
                                                                    return {
                                                                        q: params.term,
                                                                        page: params.page
                                                                    };
                                                                },
                                                                processResults: function (data, params) {
                                                                    params.page = params.page || 1;

                                                                    return {
                                                                        results: data.map(function (item) {
                                                                            return {
                                                                                id: item.id,
                                                                                text: item.name,
                                                                                stateId: item.state.id,
                                                                                stateAbbreviation: item.state.abbreviation
                                                                            }
                                                                        }),
                                                                        pagination: {
                                                                            more: (params.page * 30) < data.total_count
                                                                        }
                                                                    };
                                                                },
                                                                cache: true
                                                            },
                                                            placeholder: 'Digite o nome de uma cidade',
                                                            minimumInputLength: 1,
                                                        });

                                                        $('#city_{{ $key }}').on('select2:select', event => {
                                                            @this.set('companies.{{ $key }}.address_city_id', event.params.data.id);
                                                            @this.set('companies.{{ $key }}.address_city_name', event.params.data.text);

                                                            @this.set('companies.{{ $key }}.address_state_id', event.params.data.stateId);
                                                            @this.set('companies.{{ $key }}.address_state_abbreviation', event.params.data.stateAbbreviation);

                                                            $('#state_{{ $key }}').append(new Option(event.params.data.stateAbbreviation, event.params.data.stateId, false, false));
                                                            $('#state_{{ $key }}').val(event.params.data.stateId);

                                                            $('#city_{{ $key }}').select2({
                                                                theme: 'bootstrap4',
                                                                language: {
                                                                    inputTooShort: function () {
                                                                        return 'Digite um ou mais caracteres';
                                                                    }
                                                                },
                                                                ajax: {
                                                                    url: "{{ route('cities.get_by_name_and_state_id') }}",
                                                                    dataType: 'json',
                                                                    delay: 250,
                                                                    data: function (params) {
                                                                        return {
                                                                            q: params.term,
                                                                            state_id: event.params.data.stateId,
                                                                            page: params.page
                                                                        };
                                                                    },
                                                                    processResults: function (data, params) {
                                                                        params.page = params.page || 1;

                                                                        return {
                                                                            results: data.map(function (item) {
                                                                                return {
                                                                                    id: item.id,
                                                                                    text: item.name,
                                                                                }
                                                                            }),
                                                                            pagination: {
                                                                                more: (params.page * 30) < data.total_count
                                                                            }
                                                                        };
                                                                    },
                                                                    cache: true
                                                                },
                                                                placeholder: 'Digite o nome de uma cidade',
                                                                minimumInputLength: 1,
                                                            });
                                                        });
                                                    </script>
                                                </div>
                                                <div class="col-sm-12 col-md-3 col-lg-3" wire:ignore>
                                                    <x-input.label :$resourceRoute :$action field="state" />
                                                    <select id="state_{{ $key }}" name="state[{{ $key }}]" class="form-control">
                                                        <option value="{{ $company['address_state_id'] }}">{{ $company['address_state_abbreviation'] }}</option>
                                                    </select>
                                                    <script>
                                                        initializeSelect2("state_{{ $key }}", "{{ route('states.get_by_abbreviation') }}", 'Digite a UF');

                                                        $('#state_{{ $key }}').on('select2:select', event => {
                                                            @this.set('companies.{{ $key }}.address_state_id', event.params.data.id);
                                                            @this.set('companies.{{ $key }}.address_state_abbreviation', event.params.data.text);

                                                            $('#city_{{ $key }}').val(null);

                                                            $('#city_{{ $key }}').select2({
                                                                theme: 'bootstrap4',
                                                                language: {
                                                                    inputTooShort: function () {
                                                                        return 'Digite um ou mais caracteres';
                                                                    }
                                                                },
                                                                ajax: {
                                                                    url: "{{ route('cities.get_by_name_and_state_id') }}",
                                                                    dataType: 'json',
                                                                    delay: 250,
                                                                    data: function (params) {
                                                                        return {
                                                                            q: params.term,
                                                                            state_id: event.params.data.id,
                                                                            page: params.page
                                                                        };
                                                                    },
                                                                    processResults: function (data, params) {
                                                                        params.page = params.page || 1;

                                                                        return {
                                                                            results: data.map(function (item) {
                                                                                return {
                                                                                    id: item.id,
                                                                                    text: item.name,
                                                                                }
                                                                            }),
                                                                            pagination: {
                                                                                more: (params.page * 30) < data.total_count
                                                                            }
                                                                        };
                                                                    },
                                                                    cache: true
                                                                },
                                                                placeholder: 'Digite o nome de uma cidade',
                                                                minimumInputLength: 1,
                                                            });
                                                        });
                                                    </script>
                                                </div>
                                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="country[{{ $key }}]" :label="__('leads.forms.fields.country')" id="country_{{ $key }}" readonly="true" value="BR" />
                                            </x-form.row>
                                        </x-form.section>
                                        <x-form.section title="Serviços" :inlineCreateButton="true">
                                            <x-slot name="inlineCreateButtonSlot">
                                                <button type="button" class="btn btn-outline-primary btn-sm shadow-sm" wire:click="addCompanyProcedure({{ $key }})">
                                                    <i class="fa fa-plus mr-1"></i> Adicionar serviço
                                                </button>
                                            </x-slot>
                                            <x-form.row>
                                                <div class="col-12">
                                                    <table width="100%" class="table-striped" style="margin-top: 2px; border: 1px solid lightgrey;">
                                                        <thead>
                                                            <th width="39%">Serviço</th>
                                                            <th width="7%">Quantidade</th>
                                                            <th width="7%">Valor unit.</th>
                                                            <th width="7%">% desc.</th>
                                                            <th width="7%">R$ desc.</th>
                                                            <th width="7%">% acrésc.</th>
                                                            <th width="7%">R$ acrésc.</th>
                                                            <th width="10%">Valor total</th>
                                                            <th width="9%">Ações</th>
                                                        </thead>
                                                        <tbody>
                                                            @foreach ($company['procedures'] as $procedureKey => $procedure)
                                                                <tr>
                                                                    <td width="39%" wire:ignore>
                                                                        <script>
                                                                            initializeSelect2("lead_proposal_company_{{ $key }}_item_procedure_id_{{ $procedureKey }}", "{{ route('procedures.get_active_by_name') }}", 'Digite o nome de um serviço');
                                                                        </script>
                                                                        <select id="lead_proposal_company_{{ $key }}_item_procedure_id_{{ $procedureKey }}" name="lead_proposal_company_item_procedure[{{ $key }}][{{ $procedureKey }}]" class="form-control">
                                                                            <option value="{{ $procedure['lead_proposal_company_item_procedure_id'] }}">{{ $procedure['lead_proposal_company_item_procedure_name'] }}</option>
                                                                        </select>
                                                                        <input id="lead_proposal_company_{{ $key }}_item_type_{{ $procedureKey }}" name="lead_proposal_company_item_type[{{ $key }}][{{ $procedureKey }}]" type="hidden">
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_quantity_{{ $procedureKey }}" name="lead_proposal_company_item_quantity[{{ $key }}][{{ $procedureKey }}]" class="form-control" value="{{ $procedure['lead_proposal_company_item_quantity'] }}" />
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_unit_amount_{{ $procedureKey }}" name="lead_proposal_company_item_unit_amount[{{ $key }}][{{ $procedureKey }}]" class="form-control" readonly="true" value="{{ format_money($procedure['lead_proposal_company_item_unit_amount']) }}" />
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_discount_percentage_{{ $procedureKey }}" name="lead_proposal_company_item_discount_percentage[{{ $key }}][{{ $procedureKey }}]" class="form-control percentage-input" value="{{ format_percentage($procedure['lead_proposal_company_item_discount_percentage']) }}" disabled="true" />
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_discount_amount_{{ $procedureKey }}" name="lead_proposal_company_item_discount_amount[{{ $key }}][{{ $procedureKey }}]" class="form-control amount-input" value="{{ format_money($procedure['lead_proposal_company_item_discount_amount']) }}" disabled="true" />
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_addition_percentage_{{ $procedureKey }}" name="lead_proposal_company_item_addition_percentage[{{ $key }}][{{ $procedureKey }}]" class="form-control percentage-input" value="{{ format_percentage($procedure['lead_proposal_company_item_addition_percentage']) }}" disabled="true" />
                                                                    </td>
                                                                    <td width="7%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_addition_amount_{{ $procedureKey }}" name="lead_proposal_company_item_addition_amount[{{ $key }}][{{ $procedureKey }}]" class="form-control amount-input" value="{{ format_money($procedure['lead_proposal_company_item_addition_amount']) }}" disabled="true" />
                                                                    </td>
                                                                    <td width="10%" wire:ignore>
                                                                        <input type="text" id="lead_proposal_company_{{ $key }}_item_total_amount_{{ $procedureKey }}" name="lead_proposal_company_item_total_amount[{{ $key }}][{{ $procedureKey }}]" class="form-control amount-input" readonly="true" value="{{ format_money($procedure['lead_proposal_company_item_total_amount']) }}" disabled="true" />
                                                                    </td>
                                                                    <td width="9%">
                                                                        <button class="btn btn-outline-secondary btn-sm shadow-sm" type="button" wire:click="openReplicateCompanyProcedureModal({{ $key }}, {{ $procedureKey }})">
                                                                            <i class="fas fa-file-alt" aria-hidden="true"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-secondary btn-sm shadow-sm" type="button" id="lead_proposal_company_{{ $key }}_item_apply_discount_{{ $procedureKey }}">
                                                                            <i class="fa fa-minus" aria-hidden="true"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-secondary btn-sm shadow-sm" type="button" id="lead_proposal_company_{{ $key }}_item_apply_addition_{{ $procedureKey }}">
                                                                            <i class="fa fa-plus" aria-hidden="true"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-danger btn-sm shadow-sm" type="button" wire:click="deleteCompanyProcedure({{ $key }}, {{ $procedureKey }})">
                                                                            <i class="fa fa-trash" aria-hidden="true"></i>
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </x-form.row>
                                        </x-form.section>
                                    </x-tabs.tab-content-item>
                                @endif
                            @endforeach
                        </x-slot>
                    </x-tabs>
                </div>
            @else
            @endif
        </x-card>
    </x-form.create-form>

    <div class="modal fade" id="add-company-modal" tabindex="-1" role="dialog" aria-labelledby="add-company-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-company-label">Adicionar unidade existente</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <x-form.row>
                        <div class="col-12">
                            <label for="existing_branch" class="mb-1">Selecione uma unidade existente</label>
                            <select name="existing_branch" id="existing_branch" class="form-control">
                                @foreach ($this->leadCompanyBranchesArray as $key => $value)
                                    @if (array_search($key, array_column($this->companies, 'branch_name')) === false)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                    </x-form.row>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">Cancelar</button>
                    <button id="add-company-default-action-submit-btn" type="button" class="btn btn-outline-primary" data-dismiss="modal">Adicionar nova unidade</button>
                    <button id="add-existing-company-default-action-submit-btn" type="button" class="btn btn-primary" data-dismiss="modal">Adicionar</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="csv-error-modal" tabindex="-1" role="dialog" aria-labelledby="csv-error-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="csv-error-label">Ops!</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <x-form.row>
                        <div class="col-12">
                            <span id="csv-error-modal-message"></span>
                        </div>
                    </x-form.row>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">Ok!</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="replicate-procedure-modal" tabindex="-1" role="dialog" aria-labelledby="replicate-procedure-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="replicate-procedure-label">Replicar serviços para unidades</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <x-form.row>
                        <div class="col-12">
                            <span><strong>Você tem certeza que deseja replicar esse serviço para todas as outras unidades?</strong></span>
                        </div>
                    </x-form.row>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">{{ __('buttons.no') }}</button>
                    <button id="replicate-procedure-default-action-submit-btn" type="button" class="btn btn-primary" data-dismiss="modal" wire:click="replicateProcedureToUnits()">{{ __('buttons.yes') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="remove-invalid-branches-modal" tabindex="-1" role="dialog" aria-labelledby="remove-invalid-branches-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="remove-invalid-branches-label">Desconsiderar filiais inválidas</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <x-form.row>
                        <div class="col-12">
                            <span><strong>Você tem certeza que deseja desconsiderar as filiais inválidas?</strong></span>
                        </div>
                    </x-form.row>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">{{ __('buttons.no') }}</button>
                    <button id="remove-invalid-branches-default-action-submit-btn" type="button" class="btn btn-primary" data-dismiss="modal" wire:click="removeInvalidBranches()">{{ __('buttons.yes') }}</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ global_asset('js/jquery.maskMoney.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script src="{{ global_asset('js/core/helpers.js') }}"></script>
    <script src="{{ global_asset('js/core/components/mask-money/mask-money-helpers.js') }}"></script>

    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function(val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function(val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            $('.phone-input').mask(SPMaskBehavior, spOptions);

            initializeSelect2('lead_company_id', "{{ route('lead_companies.get_by_name_trading_name_or_tax_id_number') }}", 'Digite a razão social, o nome fantasia ou o CNPJ de uma empresa');

            $('#lead_company_id').on('select2:select', event => @this.set('leadCompanyId', event.params.data.id));

            window.livewire.on('openReplicateCompanyProcedureModal', () => {
                $('#replicate-procedure-modal').modal();
            });

            window.livewire.on('addCompanyModalOpened', () => {
                $('#add-company-modal').modal();
            });

            window.livewire.on('showCsvErrorModal', message => {
                $('#csv-error-modal-message').text(message);
                $('#csv-error-modal').modal();
            });

            $('#add-existing-company-default-action-submit-btn').click(() => {
                @this.addCompany($('#existing_branch').val());
            });

            $('#add-company-default-action-submit-btn').click(() => {
                @this.addCompany(null);
            });

            window.livewire.on('proposalItemAdded', event => {
                maskDefault('amount-input', true);
                maskPercentage('percentage-input', 4, true);

                initializeSelect2(`lead_proposal_item_procedure_id_${event.count}`, "{{ route('procedures.get_active_by_name') }}", 'Digite o nome de um serviço');

                $(`#lead_proposal_item_minimum_amount_${event.count}`).on('blur', () => {
                    const minimumAmount = $(`#lead_proposal_item_minimum_amount_${event.count}`).maskMoney('unmasked')[0];
                    @this.set(`procedures.${event.count}.lead_proposal_item_minimum_amount`, minimumAmount);
                });

                $(`#lead_proposal_item_discount_percentage_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_item_total_amount_${event.count}`);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                        return;
                    }

                    additionPercentage = 0;
                    additionAmount = 0;

                    const discountedUnitAmount = unitAmount * (1 - (discountPercentage / 100));
                    const totalAmount = quantity * discountedUnitAmount;

                    discountAmount = ((unitAmount - discountedUnitAmount) * 100) / 100;

                    $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_amount_${event.count}`).val((Math.round(discountAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_item_addition_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_item_total_amount_${event.count}`);

                    @this.set(`procedures.${event.count}.lead_proposal_item_quantity`, $(`#lead_proposal_item_quantity_${event.count}`).val());
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, discountPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, discountAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, additionPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, additionAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_item_discount_amount_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_item_total_amount_${event.count}`);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                        return;
                    }

                    additionPercentage = 0;
                    additionAmount = 0;

                    const totalAmount = quantity * (unitAmount - discountAmount);

                    discountPercentage = (discountAmount / unitAmount) * 100;

                    $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_percentage_${event.count}`).val((Math.round(discountPercentage * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_item_addition_amount_${event.count}`).val(0);

                    maskPercentage(`lead_proposal_item_discount_percentage_${event.count}`, 4);
                    maskPercentage(`lead_proposal_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_item_total_amount_${event.count}`);

                    @this.set(`procedures.${event.count}.lead_proposal_item_quantity`, $(`#lead_proposal_item_quantity_${event.count}`).val());
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, discountPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, discountAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_item_addition_percentage_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_item_total_amount_${event.count}`);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                        return;
                    }

                    discountPercentage = 0;
                    discountAmount = 0;

                    const unitAmountWithAddition = unitAmount * (1 + (additionPercentage / 100));
                    const totalAmount = quantity * unitAmountWithAddition;

                    additionAmount = ((unitAmountWithAddition - unitAmount) * 100) / 100;

                    $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_amount_${event.count}`).val((Math.round(additionAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_item_discount_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_item_total_amount_${event.count}`);

                    @this.set(`procedures.${event.count}.lead_proposal_item_quantity`, $(`#lead_proposal_item_quantity_${event.count}`).val());
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, discountPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, discountAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, additionPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, additionAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_item_addition_amount_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_item_total_amount_${event.count}`);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, 0);
                        @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                        return;
                    }

                    discountPercentage = 0;
                    discountAmount = 0;

                    const totalAmount = quantity * (unitAmount + additionAmount);

                    additionPercentage = (additionAmount / unitAmount) * 100;

                    $(`#lead_proposal_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_percentage_${event.count}`).val((Math.round(additionPercentage * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_item_discount_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_item_total_amount_${event.count}`);

                    @this.set(`procedures.${event.count}.lead_proposal_item_quantity`, $(`#lead_proposal_item_quantity_${event.count}`).val());
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_percentage`, discountPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_discount_amount`, discountAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_percentage`, additionPercentage);
                    @this.set(`procedures.${event.count}.lead_proposal_item_addition_amount`, additionAmount);
                    @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_item_procedure_id_${event.count}`).on('select2:select', selectEvent => {
                    $(`lead_proposal_item_procedure_name_${event.count}`).val(selectEvent.params.data.text);

                    @this.set(`procedures.${event.count}.lead_proposal_item_procedure_id`, selectEvent.params.data.id);
                    @this.set(`procedures.${event.count}.lead_proposal_item_procedure_name`, selectEvent.params.data.text);

                    $.ajax("{{ route('procedures.get_data_for_crm') }}", {
                        data: {
                            '_token': "{{ csrf_token() }}",
                            procedure_id: $(`#lead_proposal_item_procedure_id_${event.count}`).val(),
                            lead_branch_city_id: null,
                            lead_branch_employee_count: $(`#total_employee_count`).val(),
                        },
                        method: 'POST',
                        success: function(response) {
                            const unitAmount = response.amount;

                            const quantity = response.procedure_type !== 'exams'
                                ? parseInt($('#total_employee_count').val())
                                : 0;

                            @this.set(`procedures.${event.count}.lead_proposal_item_type`, response.type);
                            @this.set(`procedures.${event.count}.lead_proposal_item_unit_amount`, response.amount);
                            @this.set(`procedures.${event.count}.lead_proposal_item_minimum_amount`, response.minimum_amount);

                            $(`#lead_proposal_item_unit_amount_${event.count}`).val((Math.round(response.amount * 100) / 100).toString().replace('.', ','));
                            $(`#lead_proposal_item_minimum_amount_${event.count}`).val((Math.round(response.minimum_amount * 100) / 100).toString().replace('.', ','));

                            totalAmount = Math.round(unitAmount * quantity * 100) / 100;
                            $(`#lead_proposal_item_total_amount_${event.count}`).val(totalAmount.toString().replace('.', ','));
                            @this.set(`procedures.${event.count}.lead_proposal_item_total_amount`, totalAmount);

                            maskDefault(`lead_proposal_item_unit_amount_${event.count}`);
                            maskDefault(`lead_proposal_item_minimum_amount_${event.count}`);
                            maskDefault(`lead_proposal_item_total_amount_${event.count}`);
                        }
                    });
                });

                $(`#lead_proposal_item_apply_discount_${event.count}`).on('click', () => {
                    $(`#lead_proposal_item_discount_percentage_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_item_discount_amount_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_item_addition_percentage_${event.count}`).prop('disabled', true);
                    $(`#lead_proposal_item_addition_amount_${event.count}`).prop('disabled', true);
                });

                $(`#lead_proposal_item_apply_addition_${event.count}`).on('click', () => {
                    $(`#lead_proposal_item_addition_percentage_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_item_addition_amount_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_item_discount_percentage_${event.count}`).prop('disabled', true);
                    $(`#lead_proposal_item_discount_amount_${event.count}`).prop('disabled', true);
                });
            });

            window.livewire.on('proposalCompanyItemAdded', event => {
                maskDefault('amount-input', true);
                maskPercentage('percentage-input', 4, true);

                initializeSelect2(`lead_proposal_company_${event.index}_item_procedure_id_${event.count}`, "{{ route('procedures.get_active_by_name') }}", 'Digite o nome de um serviço');

                $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).on('blur', () => {
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_quantity`, $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val());

                    const quantity = $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];
                    const discountPercentage = $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    const additionPercentage = $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    const totalAmount = (quantity * unitAmount) * (1 - (discountPercentage / 100));
                    const totalAmountWithAddition = totalAmount * (1 + additionPercentage);
                    const discountAmount = (quantity * unitAmount) - totalAmount;
                    const additionAmount = totalAmountWithAddition - totalAmount;

                    $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmountWithAddition * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).val((Math.round(discountAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).val((Math.round(additionAmount * 100) / 100).toString().replace('.', ','));

                    maskDefault(`lead_proposal_company_${event.index}_item_discount_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, discountPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, discountAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, additionPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, additionAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmountWithAddition);
                });

                $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                        return;
                    }

                    additionPercentage = 0;
                    additionAmount = 0;

                    const discountedUnitAmount = unitAmount * (1 - (discountPercentage / 100));
                    const totalAmount = quantity * discountedUnitAmount;

                    discountAmount = ((unitAmount - discountedUnitAmount) * 100) / 100;

                    $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).val((Math.round(discountAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_quantity`, $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val());
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, discountPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, discountAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, additionPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, additionAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                        return;
                    }

                    additionPercentage = 0;
                    additionAmount = 0;

                    const totalAmount = quantity * (unitAmount - discountAmount);

                    discountPercentage = (discountAmount / unitAmount) * 100;

                    $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).val((Math.round(discountPercentage * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).val(0);

                    maskPercentage(`lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`, 4);
                    maskPercentage(`lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_quantity`, $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val());
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, discountPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, discountAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                        return;
                    }

                    discountPercentage = 0;
                    discountAmount = 0;

                    const unitAmountWithAddition = unitAmount * (1 + (additionPercentage / 100));
                    const totalAmount = quantity * unitAmountWithAddition;

                    additionAmount = ((unitAmountWithAddition - unitAmount) * 100) / 100;

                    $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).val((Math.round(additionAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_quantity`, $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val());
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, discountPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, discountAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, additionPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, additionAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).on('blur', () => {
                    const quantity = $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val();
                    const unitAmount = $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).maskMoney('unmasked')[0];

                    let additionPercentage = $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let additionAmount = $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).maskMoney('unmasked')[0];
                    let discountPercentage = $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).maskMoney('unmasked')[0];
                    let discountAmount = $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).maskMoney('unmasked')[0];

                    if (additionPercentage === 0.0 && additionAmount === 0.0 && discountPercentage === 0.0 && discountAmount === 0.0) {
                        const totalAmount = quantity * unitAmount;
                        $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                        maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, 0);
                        @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                        return;
                    }

                    discountPercentage = 0;
                    discountAmount = 0;

                    const totalAmount = quantity * (unitAmount + additionAmount);

                    additionPercentage = (additionAmount / unitAmount) * 100;

                    $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).val((Math.round(additionPercentage * 100) / 100).toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).val(0);
                    $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).val(0);

                    maskDefault(`lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_discount_amount_${event.count}`);
                    maskPercentage(`lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`, 4);
                    maskDefault(`lead_proposal_company_${event.index}_item_addition_amount_${event.count}`);
                    maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_quantity`, $(`#lead_proposal_company_${event.index}_item_quantity_${event.count}`).val());
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_percentage`, discountPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_discount_amount`, discountAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_percentage`, additionPercentage);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_addition_amount`, additionAmount);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);
                });

                $(`#lead_proposal_company_${event.index}_item_procedure_id_${event.count}`).on('select2:select', selectEvent => {
                    $(`lead_proposal_company_${event.index}_item_procedure_name_${event.count}`).val(selectEvent.params.data.text);

                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_procedure_id`, selectEvent.params.data.id);
                    @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_procedure_name`, selectEvent.params.data.text);

                    $.ajax("{{ route('procedures.get_data_for_crm') }}", {
                        data: {
                            '_token': "{{ csrf_token() }}",
                            procedure_id: $(`#lead_proposal_company_${event.index}_item_procedure_id_${event.count}`).val(),
                            lead_branch_city_id: $(`#city_${event.index}`).val(),
                            lead_branch_employee_count: $(`#employee_count_${event.index}`).val(),
                        },
                        method: 'POST',
                        success: function(response) {
                            const unitAmount = response.amount;
                            const quantity = response.range_type !== 'fixed' ? parseInt($('#total_employee_count').val()) : 1;

                            @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_type`, response.type);
                            @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_unit_amount`, response.amount);

                            $(`#lead_proposal_company_${event.index}_item_unit_amount_${event.count}`).val((Math.round(response.amount * 100) / 100).toString().replace('.', ','));

                            totalAmount = Math.round(unitAmount * quantity * 100) / 100;

                            $(`#lead_proposal_company_${event.index}_item_total_amount_${event.count}`).val(totalAmount.toString().replace('.', ','));
                            @this.set(`companies.${event.index}.procedures.${event.count}.lead_proposal_company_item_total_amount`, totalAmount);

                            maskDefault(`lead_proposal_company_${event.index}_item_unit_amount_${event.count}`);
                            maskDefault(`lead_proposal_company_${event.index}_item_total_amount_${event.count}`);
                        }
                    });
                });

                $(`#lead_proposal_company_${event.index}_item_apply_discount_${event.count}`).on('click', () => {
                        $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).removeAttr('disabled');
                        $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).removeAttr('disabled');
                        $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).prop('disabled', true);
                        $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).prop('disabled', true);
                });

                $(`#lead_proposal_company_${event.index}_item_apply_addition_${event.count}`).on('click', () => {
                    $(`#lead_proposal_company_${event.index}_item_addition_percentage_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_company_${event.index}_item_addition_amount_${event.count}`).removeAttr('disabled');
                    $(`#lead_proposal_company_${event.index}_item_discount_percentage_${event.count}`).prop('disabled', true);
                    $(`#lead_proposal_company_${event.index}_item_discount_amount_${event.count}`).prop('disabled', true);
                });
            });

            window.livewire.on('updateProceduresEmployeeCount', employeeCount => {
                $('#total_employee_count').val(employeeCount);

                Array.from(document.getElementsByClassName('lead-proposal-item-quantity')).forEach(item => {
                    item.value = employeeCount;

                    const quantity = employeeCount;
                    const unitAmount = $(`#lead_proposal_item_unit_amount_${item.dataset.procedureKey}`).maskMoney('unmasked')[0];
                    const discountAmount = $(`#lead_proposal_item_discount_amount_${item.dataset.procedureKey}`).maskMoney('unmasked')[0];
                    const additionAmount = $(`#lead_proposal_item_addition_amount_${item.dataset.procedureKey}`).maskMoney('unmasked')[0];
                    const totalAmount = quantity * (unitAmount - discountAmount + additionAmount);

                    $(`#lead_proposal_item_total_amount_${item.dataset.procedureKey}`).val((Math.round(totalAmount * 100) / 100).toString().replace('.', ','));

                    maskDefault(`lead_proposal_item_total_amount_${item.dataset.procedureKey}`);

                    @this.set(`procedures.${item.dataset.procedureKey}.lead_proposal_item_total_amount`, totalAmount);
                });
            });

            window.livewire.on('proposalItemDeleted', procedures => {
                Object.values(procedures).forEach((item, index) => {
                    $(`#lead_proposal_item_id_${index}`).val(item.lead_proposal_item_id);
                    $(`#lead_proposal_item_procedure_id_${index}`).val(item.lead_proposal_item_procedure_id);
                    $(`#lead_proposal_item_type_${index}`).val(item.lead_proposal_item_type);
                    $(`#lead_proposal_item_unit_amount_${index}`).val(item.lead_proposal_item_unit_amount.toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_percentage_${index}`).val(item.lead_proposal_item_discount_percentage.toString().replace('.', ','));
                    $(`#lead_proposal_item_discount_amount_${index}`).val(item.lead_proposal_item_discount_amount.toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_percentage_${index}`).val(item.lead_proposal_item_addition_percentage.toString().replace('.', ','));
                    $(`#lead_proposal_item_addition_amount_${index}`).val(item.lead_proposal_item_addition_amount.toString().replace('.', ','));
                    $(`#lead_proposal_item_total_amount_${index}`).val(item.lead_proposal_item_total_amount.toString().replace('.', ','));

                    $(`#lead_proposal_item_procedure_id_${index}`).empty();
                    $(`#lead_proposal_item_procedure_id_${index}`).append(new Option(item.lead_proposal_item_procedure_name, item.lead_proposal_item_procedure_id)).trigger('change');

                    maskDefault(`lead_proposal_item_unit_amount_${index}`);
                    maskPercentage(`lead_proposal_item_discount_percentage_${index}`, 4);
                    maskDefault(`lead_proposal_item_discount_amount_${index}`);
                    maskPercentage(`lead_proposal_item_addition_percentage_${index}`, 4);
                    maskDefault(`lead_proposal_item_addition_amount_${index}`);
                    maskDefault(`lead_proposal_item_total_amount_${index}`);
                });
            });

            window.livewire.on('proposalCompanyItemDeleted', event => {
                Object.values(event.procedures).forEach((item, index) => {
                    $(`#lead_proposal_company_${event.companyIndex}_item_id_${index}`).val(item.lead_proposal_company_item_id);
                    $(`#lead_proposal_company_${event.companyIndex}_item_procedure_id_${index}`).val(item.lead_proposal_company_item_procedure_id);
                    $(`#lead_proposal_company_${event.companyIndex}_item_type_${index}`).val(item.lead_proposal_company_item_type);
                    $(`#lead_proposal_company_${event.companyIndex}_item_quantity_${index}`).val(item.lead_proposal_company_item_quantity);
                    $(`#lead_proposal_company_${event.companyIndex}_item_unit_amount_${index}`).val(item.lead_proposal_company_item_unit_amount.toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.companyIndex}_item_discount_percentage_${index}`).val(item.lead_proposal_company_item_discount_percentage.toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.companyIndex}_item_discount_amount_${index}`).val(item.lead_proposal_company_item_discount_amount.toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.companyIndex}_item_addition_percentage_${index}`).val(item.lead_proposal_company_item_addition_percentage.toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.companyIndex}_item_addition_amount_${index}`).val(item.lead_proposal_company_item_addition_amount.toString().replace('.', ','));
                    $(`#lead_proposal_company_${event.companyIndex}_item_total_amount_${index}`).val(item.lead_proposal_company_item_total_amount.toString().replace('.', ','));

                    $(`#lead_proposal_company_${event.companyIndex}_item_procedure_id_${index}`).empty();
                    $(`#lead_proposal_company_${event.companyIndex}_item_procedure_id_${index}`).append(new Option(item.lead_proposal_company_item_procedure_name, item.lead_proposal_company_item_procedure_id)).trigger('change');

                    maskDefault(`lead_proposal_company_${event.companyIndex}_item_unit_amount_${index}`);
                    maskPercentage(`lead_proposal_company_${event.companyIndex}_item_discount_percentage_${index}`, 4);
                    maskDefault(`lead_proposal_company_${event.companyIndex}_item_discount_amount_${index}`);
                    maskPercentage(`lead_proposal_company_${event.companyIndex}_item_addition_percentage_${index}`, 4);
                    maskDefault(`lead_proposal_company_${event.companyIndex}_item_addition_amount_${index}`);
                    maskDefault(`lead_proposal_company_${event.companyIndex}_item_total_amount_${index}`);
                });
            });
        });
    </script>
</div>
