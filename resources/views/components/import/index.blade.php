@props(['title', 'route', 'hideBody' => false])

<div class="px-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card-header d-flex align-items-center">
                <strong class="card-header-title">{{ $title }}</strong>
                <div class="ml-auto">
                    <x-button.back />
                </div>
            </div>

            <form action="{{ $route }}" method="POST" target="_blank" enctype="multipart/form-data">
                @csrf

                @if (!$hideBody)
                <x-import.body>
                    <div class="row">
                        <div class="col-12">
                            <label for="file" class="mb-0">Arquivo</label><span class="text-danger"><strong>*</strong></span>
                            <input type="file" name="file" class="input-group control-group form-control file-input" accept=".xlsx">
                            <span class="mt-5 small text-danger"><strong>Atenção:</strong> somente serão aceitos arquivos com extensão <strong>.xlsx</strong>!</span>
                        </div>
                    </div>
                </x-import.body>
                @endif

                <x-import.footer>
                    {{ $slot }}
                </x-import.footer>
            </form>
        </div>
    </div>
</div>
