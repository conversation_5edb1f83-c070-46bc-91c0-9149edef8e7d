@props([
    'title' => '',
    'main' => true,
    'marginTop' => false,
    'cardHeaderTitle' => false,
    'createsResource' => false,
    'createResourceRoute' => null,
    'smallCreateButton' => true,
    'backButton' => false,
    'backRoute' => null,
    'actions' => false,
    'importsResource' => false,
    'exportsResource' => false,
    'inlineCreateButton' => false,
])

<div class="@if ($main) px-4 @else container @endif @if ($marginTop) mt-4 @endif">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card-header d-flex align-items-center">
                <div>
                    <strong @if ($cardHeaderTitle) class="card-header-title" @endif>{{ $title }}</strong>
                    <div id="badges">
                        {{ $badges ?? '' }}
                    </div>
                </div>
                <div class="d-flex ml-auto">
                    @if ($actions)
                    <div class="dropdown ml-2">
                        <button class="btn @if (!$cardHeaderTitle) btn-sm @endif btn-outline-secondary @if (!$main) btn-sm @endif dropdown-toggle mr-2" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-cog mr-1"></i> Ações
                        </button>
                        <div class="dropdown-menu dropdown-menu-right shadow-sm" aria-labelledby="dropdownMenuButton">
                            {{ $actionLinks ?? ''}}
                            @if ($importsResource)<button class="dropdown-item" data-toggle="modal" data-target="#import-modal">Importar</button>@endif
                            @if ($exportsResource)<button class="dropdown-item" data-toggle="modal" data-target="#export-modal">Exportar</button>@endif
                        </div>
                    </div>
                    @endif
                    {{ $actionsDiv ?? '' }}
                    @if ($createsResource)
                        <x-button.create route="{{ $createResourceRoute }}" :small="$smallCreateButton" />
                    @endif
                    @if ($inlineCreateButton)
                        {{ $inlineCreateButtonSlot ?? '' }}
                    @endif
                    @if ($backButton)
                        <x-button.back :route="$backRoute ?? url()->previous()" />
                    @endif
                </div>
            </div>

            <x-action-card.body>
                {{ $slot }}
            </x-action-card.body>
        </div>
    </div>
</div>
