@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <x-action-card.header :resourceRoute="$resourceRoute" backRoute="{{ route('roles.index') }}" :action="$action"></x-action-card.header>
                <div>
                    <fieldset disabled>
                        <div>
                            <div class="card-header d-flex align-items-center">
                                <strong>Geral</strong>
                            </div>
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <label class="mb-1" for="name">{{ __('roles.cards.edit.body.fields.name') }}</label>
                                            <input type="text" class="form-control" id="name" name="name" value="{{ $role->name }}" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="card-header d-flex align-items-center">
                                <strong>Per<PERSON><PERSON><PERSON>es</strong>
                            </div>
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div>
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Controle de acesso</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_roles" name="get_roles" @if ($role->hasPermissionTo('get_roles')) checked @endif>
                                                <label for="get_roles">{{ __('permissions.get_roles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_roles" name="create_roles" @if ($role->hasPermissionTo('create_roles')) checked @endif>
                                                <label for="create_roles">{{ __('permissions.create_roles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_roles" name="update_roles" @if ($role->hasPermissionTo('update_roles')) checked @endif>
                                                <label for="update_roles">{{ __('permissions.update_roles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_roles" name="delete_roles" @if ($role->hasPermissionTo('delete_roles')) checked @endif>
                                                <label for="delete_roles">{{ __('permissions.delete_roles') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_operators" name="get_operators" @if ($role->hasPermissionTo('get_operators')) checked @endif>
                                                <label for="get_operators">{{ __('permissions.get_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_operators" name="create_operators" @if ($role->hasPermissionTo('create_operators')) checked @endif>
                                                <label for="create_operators">{{ __('permissions.create_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_operators" name="update_operators" @if ($role->hasPermissionTo('update_operators')) checked @endif>
                                                <label for="update_operators">{{ __('permissions.update_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_operators" name="delete_operators" @if ($role->hasPermissionTo('delete_operators')) checked @endif>
                                                <label for="delete_operators">{{ __('permissions.delete_operators') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Cadastros</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_checklists" name="get_checklists" @if ($role->hasPermissionTo('get_checklists')) checked @endif>
                                                <label for="get_checklists">{{ __('permissions.get_checklists') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_checklists" name="create_checklists" @if ($role->hasPermissionTo('create_checklists')) checked @endif>
                                                <label for="create_checklists">{{ __('permissions.create_checklists') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_checklists" name="update_checklists" @if ($role->hasPermissionTo('update_checklists')) checked @endif>
                                                <label for="update_checklists">{{ __('permissions.update_checklists') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_checklists" name="delete_checklists" @if ($role->hasPermissionTo('delete_checklists')) checked @endif>
                                                <label for="delete_checklists">{{ __('permissions.delete_checklists') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_checklist_phases" name="get_checklist_phases" @if ($role->hasPermissionTo('get_checklist_phases')) checked @endif>
                                                <label for="get_checklist_phases">{{ __('permissions.get_checklist_phases') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_checklist_phases" name="create_checklist_phases" @if ($role->hasPermissionTo('create_checklist_phases')) checked @endif>
                                                <label for="create_checklist_phases">{{ __('permissions.create_checklist_phases') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_checklist_phases" name="update_checklist_phases" @if ($role->hasPermissionTo('update_checklist_phases')) checked @endif>
                                                <label for="update_checklist_phases">{{ __('permissions.update_checklist_phases') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_checklist_phases" name="delete_checklist_phases" @if ($role->hasPermissionTo('delete_checklist_phases')) checked @endif>
                                                <label for="delete_checklist_phases">{{ __('permissions.delete_checklist_phases') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_checklist_phase_items" name="get_checklist_phase_items" @if ($role->hasPermissionTo('get_checklist_phase_items')) checked @endif>
                                                <label for="get_checklist_phase_items">{{ __('permissions.get_checklist_phase_items') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_checklist_phase_items" name="create_checklist_phase_items" @if ($role->hasPermissionTo('create_checklist_phase_items')) checked @endif>
                                                <label for="create_checklist_phase_items">{{ __('permissions.create_checklist_phase_items') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_checklist_phase_items" name="update_checklist_phase_items" @if ($role->hasPermissionTo('update_checklist_phase_items')) checked @endif>
                                                <label for="update_checklist_phase_items">{{ __('permissions.update_checklist_phase_items') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_checklist_phase_items" name="delete_checklist_phase_items" @if ($role->hasPermissionTo('delete_checklist_phase_items')) checked @endif>
                                                <label for="delete_checklist_phase_items">{{ __('permissions.delete_checklist_phase_items') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_holidays" name="get_holidays" @if ($role->hasPermissionTo('get_holidays')) checked @endif>
                                                <label for="get_holidays">{{ __('permissions.get_holidays') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_holidays" name="create_holidays" @if ($role->hasPermissionTo('create_holidays')) checked @endif>
                                                <label for="create_holidays">{{ __('permissions.create_holidays') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_holidays" name="update_holidays" @if ($role->hasPermissionTo('update_holidays')) checked @endif>
                                                <label for="update_holidays">{{ __('permissions.update_holidays') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_holidays" name="delete_holidays" @if ($role->hasPermissionTo('delete_holidays')) checked @endif>
                                                <label for="delete_holidays">{{ __('permissions.delete_holidays') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_indices" name="get_indices" @if ($role->hasPermissionTo('get_indices')) checked @endif>
                                                <label for="get_indices">{{ __('permissions.get_indices') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_indices" name="create_indices" @if ($role->hasPermissionTo('create_indices')) checked @endif>
                                                <label for="create_indices">{{ __('permissions.create_indices') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_indices" name="update_indices" @if ($role->hasPermissionTo('update_indices')) checked @endif>
                                                <label for="update_indices">{{ __('permissions.update_indices') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_indices" name="delete_indices" @if ($role->hasPermissionTo('delete_indices')) checked @endif>
                                                <label for="delete_indices">{{ __('permissions.delete_indices') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_procedures" name="get_procedures" @if ($role->hasPermissionTo('get_procedures')) checked @endif>
                                                <label for="get_procedures">{{ __('permissions.get_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_procedures" name="create_procedures" @if ($role->hasPermissionTo('create_procedures')) checked @endif>
                                                <label for="create_procedures">{{ __('permissions.create_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_procedures" name="update_procedures" @if ($role->hasPermissionTo('update_procedures')) checked @endif>
                                                <label for="update_procedures">{{ __('permissions.update_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_procedures" name="delete_procedures" @if ($role->hasPermissionTo('delete_procedures')) checked @endif>
                                                <label for="delete_procedures">{{ __('permissions.delete_procedures') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_procedure_payment_methods" name="get_procedure_payment_methods" @if ($role->hasPermissionTo('get_procedure_payment_methods')) checked @endif>
                                                <label for="get_procedure_payment_methods">{{ __('permissions.get_procedure_payment_methods') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_procedure_payment_methods" name="create_procedure_payment_methods" @if ($role->hasPermissionTo('create_procedure_payment_methods')) checked @endif>
                                                <label for="create_procedure_payment_methods">{{ __('permissions.create_procedure_payment_methods') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_procedure_payment_methods" name="update_procedure_payment_methods" @if ($role->hasPermissionTo('update_procedure_payment_methods')) checked @endif>
                                                <label for="update_procedure_payment_methods">{{ __('permissions.update_procedure_payment_methods') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_procedure_payment_methods" name="delete_procedure_payment_methods" @if ($role->hasPermissionTo('delete_procedure_payment_methods')) checked @endif>
                                                <label for="delete_procedure_payment_methods">{{ __('permissions.delete_procedure_payment_methods') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_expense_types" name="get_expense_types" @if ($role->hasPermissionTo('get_expense_types')) checked @endif>
                                                <label for="get_expense_types">{{ __('permissions.get_expense_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_expense_types" name="create_expense_types" @if ($role->hasPermissionTo('create_expense_types')) checked @endif>
                                                <label for="create_expense_types">{{ __('permissions.create_expense_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_expense_types" name="update_expense_types" @if ($role->hasPermissionTo('update_expense_types')) checked @endif>
                                                <label for="update_expense_types">{{ __('permissions.update_expense_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_expense_types" name="delete_expense_types" @if ($role->hasPermissionTo('delete_expense_types')) checked @endif>
                                                <label for="delete_expense_types">{{ __('permissions.delete_expense_types') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_contract_types" name="get_contract_types" @if ($role->hasPermissionTo('get_contract_types')) checked @endif>
                                                <label for="get_contract_types">{{ __('permissions.get_contract_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_contract_types" name="create_contract_types" @if ($role->hasPermissionTo('create_contract_types')) checked @endif>
                                                <label for="create_contract_types">{{ __('permissions.create_contract_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_contract_types" name="update_contract_types" @if ($role->hasPermissionTo('update_contract_types')) checked @endif>
                                                <label for="update_contract_types">{{ __('permissions.update_contract_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_contract_types" name="delete_contract_types" @if ($role->hasPermissionTo('delete_contract_types')) checked @endif>
                                                <label for="delete_contract_types">{{ __('permissions.delete_contract_types') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_reasons" name="get_reasons" @if ($role->hasPermissionTo('get_reasons')) checked @endif>
                                                <label for="get_reasons">{{ __('permissions.get_reasons') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_reasons" name="create_reasons" @if ($role->hasPermissionTo('create_reasons')) checked @endif>
                                                <label for="create_reasons">{{ __('permissions.create_reasons') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_reasons" name="update_reasons" @if ($role->hasPermissionTo('update_reasons')) checked @endif>
                                                <label for="update_reasons">{{ __('permissions.update_reasons') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_reasons" name="delete_reasons" @if ($role->hasPermissionTo('delete_reasons')) checked @endif>
                                                <label for="delete_reasons">{{ __('permissions.delete_reasons') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Entidades</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_companies" name="get_companies" @if ($role->hasPermissionTo('get_companies')) checked @endif>
                                                <label for="get_companies">{{ __('permissions.get_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_companies" name="create_companies" @if ($role->hasPermissionTo('create_companies')) checked @endif>
                                                <label for="create_companies">{{ __('permissions.create_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_companies" name="update_companies" @if ($role->hasPermissionTo('update_companies')) checked @endif>
                                                <label for="update_companies">{{ __('permissions.update_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_companies" name="delete_companies" @if ($role->hasPermissionTo('delete_companies')) checked @endif>
                                                <label for="delete_companies">{{ __('permissions.delete_companies') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_pre_companies" name="get_pre_companies" @if ($role->hasPermissionTo('get_pre_companies')) checked @endif>
                                                <label for="get_pre_companies">{{ __('permissions.get_pre_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_pre_companies" name="create_pre_companies" @if ($role->hasPermissionTo('create_pre_companies')) checked @endif>
                                                <label for="create_pre_companies">{{ __('permissions.create_pre_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_pre_companies" name="update_pre_companies" @if ($role->hasPermissionTo('update_pre_companies')) checked @endif>
                                                <label for="update_pre_companies">{{ __('permissions.update_pre_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_pre_companies" name="delete_pre_companies" @if ($role->hasPermissionTo('delete_pre_companies')) checked @endif>
                                                <label for="delete_pre_companies">{{ __('permissions.delete_pre_companies') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_contacts" name="get_company_contacts" @if ($role->hasPermissionTo('get_company_contacts')) checked @endif>
                                                <label for="get_company_contacts">{{ __('permissions.get_company_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_contacts" name="create_company_contacts" @if ($role->hasPermissionTo('create_company_contacts')) checked @endif>
                                                <label for="create_company_contacts">{{ __('permissions.create_company_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_contacts" name="update_company_contacts" @if ($role->hasPermissionTo('update_company_contacts')) checked @endif>
                                                <label for="update_company_contacts">{{ __('permissions.update_company_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_contacts" name="delete_company_contacts" @if ($role->hasPermissionTo('delete_company_contacts')) checked @endif>
                                                <label for="delete_company_contacts">{{ __('permissions.delete_company_contacts') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_groups" name="get_company_groups" @if ($role->hasPermissionTo('get_company_groups')) checked @endif>
                                                <label for="get_company_groups">{{ __('permissions.get_company_groups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_groups" name="create_company_groups" @if ($role->hasPermissionTo('create_company_groups')) checked @endif>
                                                <label for="create_company_groups">{{ __('permissions.create_company_groups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_groups" name="update_company_groups" @if ($role->hasPermissionTo('update_company_groups')) checked @endif>
                                                <label for="update_company_groups">{{ __('permissions.update_company_groups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_groups" name="delete_company_groups" @if ($role->hasPermissionTo('delete_company_groups')) checked @endif>
                                                <label for="delete_company_groups">{{ __('permissions.delete_company_groups') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_group_companies" name="get_company_group_companies" @if ($role->hasPermissionTo('get_company_group_companies')) checked @endif>
                                                <label for="get_company_group_companies">{{ __('permissions.get_company_group_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_group_companies" name="create_company_group_companies" @if ($role->hasPermissionTo('create_company_group_companies')) checked @endif>
                                                <label for="create_company_group_companies">{{ __('permissions.create_company_group_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_group_companies" name="update_company_group_companies" @if ($role->hasPermissionTo('update_company_group_companies')) checked @endif>
                                                <label for="update_company_group_companies">{{ __('permissions.update_company_group_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_group_companies" name="delete_company_group_companies" @if ($role->hasPermissionTo('delete_company_group_companies')) checked @endif>
                                                <label for="delete_company_group_companies">{{ __('permissions.delete_company_group_companies') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_suppliers" name="get_suppliers" @if ($role->hasPermissionTo('get_suppliers')) checked @endif>
                                                <label for="get_suppliers">{{ __('permissions.get_suppliers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_suppliers" name="create_suppliers" @if ($role->hasPermissionTo('create_suppliers')) checked @endif>
                                                <label for="create_suppliers">{{ __('permissions.create_suppliers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_suppliers" name="update_suppliers" @if ($role->hasPermissionTo('update_suppliers')) checked @endif>
                                                <label for="update_suppliers">{{ __('permissions.update_suppliers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_suppliers" name="delete_suppliers" @if ($role->hasPermissionTo('delete_suppliers')) checked @endif>
                                                <label for="delete_suppliers">{{ __('permissions.delete_suppliers') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_providers" name="get_providers" @if ($role->hasPermissionTo('get_providers')) checked @endif>
                                                <label for="get_providers">{{ __('permissions.get_providers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_providers" name="create_providers" @if ($role->hasPermissionTo('create_providers')) checked @endif>
                                                <label for="create_providers">{{ __('permissions.create_providers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_providers" name="update_providers" @if ($role->hasPermissionTo('update_providers')) checked @endif>
                                                <label for="update_providers">{{ __('permissions.update_providers') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_providers" name="delete_providers" @if ($role->hasPermissionTo('delete_providers')) checked @endif>
                                                <label for="delete_providers">{{ __('permissions.delete_providers') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_files" name="get_provider_files" @if ($role->hasPermissionTo('get_provider_files')) checked @endif>
                                                <label for="get_provider_files">{{ __('permissions.get_provider_files') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_files" name="create_provider_files" @if ($role->hasPermissionTo('create_provider_files')) checked @endif>
                                                <label for="create_provider_files">{{ __('permissions.create_provider_files') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_files" name="update_provider_files" @if ($role->hasPermissionTo('update_provider_files')) checked @endif>
                                                <label for="update_provider_files">{{ __('permissions.update_provider_files') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_files" name="delete_provider_files" @if ($role->hasPermissionTo('delete_provider_files')) checked @endif>
                                                <label for="delete_provider_files">{{ __('permissions.delete_provider_files') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_contacts" name="get_provider_contacts" @if ($role->hasPermissionTo('get_provider_contacts')) checked @endif>
                                                <label for="get_provider_contacts">{{ __('permissions.get_provider_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_contacts" name="create_provider_contacts" @if ($role->hasPermissionTo('create_provider_contacts')) checked @endif>
                                                <label for="create_provider_contacts">{{ __('permissions.create_provider_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_contacts" name="update_provider_contacts" @if ($role->hasPermissionTo('update_provider_contacts')) checked @endif>
                                                <label for="update_provider_contacts">{{ __('permissions.update_provider_contacts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_contacts" name="delete_provider_contacts" @if ($role->hasPermissionTo('delete_provider_contacts')) checked @endif>
                                                <label for="delete_provider_contacts">{{ __('permissions.delete_provider_contacts') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_procedures" name="get_provider_procedures" @if ($role->hasPermissionTo('get_provider_procedures')) checked @endif>
                                                <label for="get_provider_procedures">{{ __('permissions.get_provider_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_procedures" name="create_provider_procedures" @if ($role->hasPermissionTo('create_provider_procedures')) checked @endif>
                                                <label for="create_provider_procedures">{{ __('permissions.create_provider_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_procedures" name="update_provider_procedures" @if ($role->hasPermissionTo('update_provider_procedures')) checked @endif>
                                                <label for="update_provider_procedures">{{ __('permissions.update_provider_procedures') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_procedures" name="delete_provider_procedures" @if ($role->hasPermissionTo('delete_provider_procedures')) checked @endif>
                                                <label for="delete_provider_procedures">{{ __('permissions.delete_provider_procedures') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_city_coverage_cities" name="get_provider_city_coverage_cities" @if ($role->hasPermissionTo('get_provider_city_coverage_cities')) checked @endif>
                                                <label for="get_provider_city_coverage_cities">{{ __('permissions.get_provider_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_city_coverage_cities" name="create_provider_city_coverage_cities" @if ($role->hasPermissionTo('create_provider_city_coverage_cities')) checked @endif>
                                                <label for="create_provider_city_coverage_cities">{{ __('permissions.create_provider_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_city_coverage_cities" name="update_provider_city_coverage_cities" @if ($role->hasPermissionTo('update_provider_city_coverage_cities')) checked @endif>
                                                <label for="update_provider_city_coverage_cities">{{ __('permissions.update_provider_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_city_coverage_cities" name="delete_provider_city_coverage_cities" @if ($role->hasPermissionTo('delete_provider_city_coverage_cities')) checked @endif>
                                                <label for="delete_provider_city_coverage_cities">{{ __('permissions.delete_provider_city_coverage_cities') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_companies" name="get_provider_companies" @if ($role->hasPermissionTo('get_provider_companies')) checked @endif>
                                                <label for="get_provider_companies">{{ __('permissions.get_provider_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_companies" name="create_provider_companies" @if ($role->hasPermissionTo('create_provider_companies')) checked @endif>
                                                <label for="create_provider_companies">{{ __('permissions.create_provider_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_companies" name="update_provider_companies" @if ($role->hasPermissionTo('update_provider_companies')) checked @endif>
                                                <label for="update_provider_companies">{{ __('permissions.update_provider_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_companies" name="delete_provider_companies" @if ($role->hasPermissionTo('delete_provider_companies')) checked @endif>
                                                <label for="delete_provider_companies">{{ __('permissions.delete_provider_companies') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_workflows" name="get_workflows" @if ($role->hasPermissionTo('get_workflows')) checked @endif>
                                                <label for="get_workflows">{{ __('permissions.get_workflows') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_workflows" name="create_workflows" @if ($role->hasPermissionTo('create_workflows')) checked @endif>
                                                <label for="create_workflows">{{ __('permissions.create_workflows') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_workflows" name="update_workflows" @if ($role->hasPermissionTo('update_workflows')) checked @endif>
                                                <label for="update_workflows">{{ __('permissions.update_workflows') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_workflows" name="delete_workflows" @if ($role->hasPermissionTo('delete_workflows')) checked @endif>
                                                <label for="delete_workflows">{{ __('permissions.delete_workflows') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_schedules" name="get_schedules" @if ($role->hasPermissionTo('get_schedules')) checked @endif>
                                                <label for="get_schedules">{{ __('permissions.get_schedules') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_schedules" name="create_schedules" @if ($role->hasPermissionTo('create_schedules')) checked @endif>
                                                <label for="create_schedules">{{ __('permissions.create_schedules') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_schedules" name="update_schedules" @if ($role->hasPermissionTo('update_schedules')) checked @endif>
                                                <label for="update_schedules">{{ __('permissions.update_schedules') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_schedules" name="delete_schedules" @if ($role->hasPermissionTo('delete_schedules')) checked @endif>
                                                <label for="delete_schedules">{{ __('permissions.delete_schedules') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Contratos</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_contracts" name="get_contracts" @if ($role->hasPermissionTo('get_contracts')) checked @endif>
                                                <label for="get_contracts">{{ __('permissions.get_contracts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_contracts" name="create_contracts" @if ($role->hasPermissionTo('create_contracts')) checked @endif>
                                                <label for="create_contracts">{{ __('permissions.create_contracts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_contracts" name="update_contracts" @if ($role->hasPermissionTo('update_contracts')) checked @endif>
                                                <label for="update_contracts">{{ __('permissions.update_contracts') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_contracts" name="delete_contracts" @if ($role->hasPermissionTo('delete_contracts')) checked @endif>
                                                <label for="delete_contracts">{{ __('permissions.delete_contracts') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Lançamentos</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_supplier_expenses" name="get_supplier_expenses" @if ($role->hasPermissionTo('get_supplier_expenses')) checked @endif>
                                                <label for="get_supplier_expenses">{{ __('permissions.get_supplier_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_supplier_expenses" name="create_supplier_expenses" @if ($role->hasPermissionTo('create_supplier_expenses')) checked @endif>
                                                <label for="create_supplier_expenses">{{ __('permissions.create_supplier_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_supplier_expenses" name="update_supplier_expenses" @if ($role->hasPermissionTo('update_supplier_expenses')) checked @endif>
                                                <label for="update_supplier_expenses">{{ __('permissions.update_supplier_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_supplier_expenses" name="delete_supplier_expenses" @if ($role->hasPermissionTo('delete_supplier_expenses')) checked @endif>
                                                <label for="delete_supplier_expenses">{{ __('permissions.delete_supplier_expenses') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_provider_expenses" name="get_provider_expenses" @if ($role->hasPermissionTo('get_provider_expenses')) checked @endif>
                                                <label for="get_provider_expenses">{{ __('permissions.get_provider_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_provider_expenses" name="create_provider_expenses" @if ($role->hasPermissionTo('create_provider_expenses')) checked @endif>
                                                <label for="create_provider_expenses">{{ __('permissions.create_provider_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_provider_expenses" name="update_provider_expenses" @if ($role->hasPermissionTo('update_provider_expenses')) checked @endif>
                                                <label for="update_provider_expenses">{{ __('permissions.update_provider_expenses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_provider_expenses" name="delete_provider_expenses" @if ($role->hasPermissionTo('delete_provider_expenses')) checked @endif>
                                                <label for="delete_provider_expenses">{{ __('permissions.delete_provider_expenses') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>CRM</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_city_coverages" name="get_crm_city_coverages" @if ($role->hasPermissionTo('get_crm_city_coverages')) checked @endif>
                                                <label for="get_crm_city_coverages">{{ __('permissions.get_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_city_coverages" name="create_crm_city_coverages" @if ($role->hasPermissionTo('create_crm_city_coverages')) checked @endif>
                                                <label for="create_crm_city_coverages">{{ __('permissions.create_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_city_coverages" name="update_crm_city_coverages" @if ($role->hasPermissionTo('update_crm_city_coverages')) checked @endif>
                                                <label for="update_crm_city_coverages">{{ __('permissions.update_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_city_coverages" name="delete_crm_city_coverages" @if ($role->hasPermissionTo('delete_crm_city_coverages')) checked @endif>
                                                <label for="delete_crm_city_coverages">{{ __('permissions.delete_crm_city_coverages') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_city_coverage_cities" name="get_crm_city_coverage_cities" @if ($role->hasPermissionTo('get_crm_city_coverage_cities')) checked @endif>
                                                <label for="get_crm_city_coverage_cities">{{ __('permissions.get_crm_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_city_coverage_cities" name="create_crm_city_coverage_cities" @if ($role->hasPermissionTo('create_crm_city_coverage_cities')) checked @endif>
                                                <label for="create_crm_city_coverage_cities">{{ __('permissions.create_crm_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_city_coverage_cities" name="update_crm_city_coverage_cities" @if ($role->hasPermissionTo('update_crm_city_coverage_cities')) checked @endif>
                                                <label for="update_crm_city_coverage_cities">{{ __('permissions.update_crm_city_coverage_cities') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_city_coverage_cities" name="delete_crm_city_coverage_cities" @if ($role->hasPermissionTo('delete_crm_city_coverage_cities')) checked @endif>
                                                <label for="delete_crm_city_coverage_cities">{{ __('permissions.delete_crm_city_coverage_cities') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_procedure_crm_city_coverages" name="get_procedure_crm_city_coverages" @if ($role->hasPermissionTo('get_procedure_crm_city_coverages')) checked @endif>
                                                <label for="get_procedure_crm_city_coverages">{{ __('permissions.get_procedure_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_procedure_crm_city_coverages" name="create_procedure_crm_city_coverages" @if ($role->hasPermissionTo('create_procedure_crm_city_coverages')) checked @endif>
                                                <label for="create_procedure_crm_city_coverages">{{ __('permissions.create_procedure_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_procedure_crm_city_coverages" name="update_procedure_crm_city_coverages" @if ($role->hasPermissionTo('update_procedure_crm_city_coverages')) checked @endif>
                                                <label for="update_procedure_crm_city_coverages">{{ __('permissions.update_procedure_crm_city_coverages') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_procedure_crm_city_coverages" name="delete_procedure_crm_city_coverages" @if ($role->hasPermissionTo('delete_procedure_crm_city_coverages')) checked @endif>
                                                <label for="delete_procedure_crm_city_coverages">{{ __('permissions.delete_procedure_crm_city_coverages') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_procedure_crm_city_coverage_ranges" name="get_procedure_crm_city_coverage_ranges" @if ($role->hasPermissionTo('get_procedure_crm_city_coverage_ranges')) checked @endif>
                                                <label for="get_procedure_crm_city_coverage_ranges">{{ __('permissions.get_procedure_crm_city_coverage_ranges') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_procedure_crm_city_coverage_ranges" name="create_procedure_crm_city_coverage_ranges" @if ($role->hasPermissionTo('create_procedure_crm_city_coverage_ranges')) checked @endif>
                                                <label for="create_procedure_crm_city_coverage_ranges">{{ __('permissions.create_procedure_crm_city_coverage_ranges') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_procedure_crm_city_coverage_ranges" name="update_procedure_crm_city_coverage_ranges" @if ($role->hasPermissionTo('update_procedure_crm_city_coverage_ranges')) checked @endif>
                                                <label for="update_procedure_crm_city_coverage_ranges">{{ __('permissions.update_procedure_crm_city_coverage_ranges') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_procedure_crm_city_coverage_ranges" name="delete_procedure_crm_city_coverage_ranges" @if ($role->hasPermissionTo('delete_procedure_crm_city_coverage_ranges')) checked @endif>
                                                <label for="delete_procedure_crm_city_coverage_ranges">{{ __('permissions.delete_procedure_crm_city_coverage_ranges') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_funnels" name="get_crm_funnels" @if ($role->hasPermissionTo('get_crm_funnels')) checked @endif>
                                                <label for="get_crm_funnels">{{ __('permissions.get_crm_funnels') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_funnels" name="create_crm_funnels" @if ($role->hasPermissionTo('create_crm_funnels')) checked @endif>
                                                <label for="create_crm_funnels">{{ __('permissions.create_crm_funnels') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_funnels" name="update_crm_funnels" @if ($role->hasPermissionTo('update_crm_funnels')) checked @endif>
                                                <label for="update_crm_funnels">{{ __('permissions.update_crm_funnels') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_funnels" name="delete_crm_funnels" @if ($role->hasPermissionTo('delete_crm_funnels')) checked @endif>
                                                <label for="delete_crm_funnels">{{ __('permissions.delete_crm_funnels') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_funnel_steps" name="get_crm_funnel_steps" @if ($role->hasPermissionTo('get_crm_funnel_steps')) checked @endif>
                                                <label for="get_crm_funnel_steps">{{ __('permissions.get_crm_funnel_steps') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_funnel_steps" name="create_crm_funnel_steps" @if ($role->hasPermissionTo('create_crm_funnel_steps')) checked @endif>
                                                <label for="create_crm_funnel_steps">{{ __('permissions.create_crm_funnel_steps') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_funnel_steps" name="update_crm_funnel_steps" @if ($role->hasPermissionTo('update_crm_funnel_steps')) checked @endif>
                                                <label for="update_crm_funnel_steps">{{ __('permissions.update_crm_funnel_steps') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_funnel_steps" name="delete_crm_funnel_steps" @if ($role->hasPermissionTo('delete_crm_funnel_steps')) checked @endif>
                                                <label for="delete_crm_funnel_steps">{{ __('permissions.delete_crm_funnel_steps') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_funnel_operators" name="get_crm_funnel_operators" @if ($role->hasPermissionTo('get_crm_funnel_operators')) checked @endif>
                                                <label for="get_crm_funnel_operators">{{ __('permissions.get_crm_funnel_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_funnel_operators" name="create_crm_funnel_operators" @if ($role->hasPermissionTo('create_crm_funnel_operators')) checked @endif>
                                                <label for="create_crm_funnel_operators">{{ __('permissions.create_crm_funnel_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_funnel_operators" name="update_crm_funnel_operators" @if ($role->hasPermissionTo('update_crm_funnel_operators')) checked @endif>
                                                <label for="update_crm_funnel_operators">{{ __('permissions.update_crm_funnel_operators') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_funnel_operators" name="delete_crm_funnel_operators" @if ($role->hasPermissionTo('delete_crm_funnel_operators')) checked @endif>
                                                <label for="delete_crm_funnel_operators">{{ __('permissions.delete_crm_funnel_operators') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_lead_companies" name="get_lead_companies" @if ($role->hasPermissionTo('get_lead_companies')) checked @endif>
                                                <label for="get_lead_companies">{{ __('permissions.get_lead_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_lead_companies" name="create_lead_companies" @if ($role->hasPermissionTo('create_lead_companies')) checked @endif>
                                                <label for="create_lead_companies">{{ __('permissions.create_lead_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_lead_companies" name="update_lead_companies" @if ($role->hasPermissionTo('update_lead_companies')) checked @endif>
                                                <label for="update_lead_companies">{{ __('permissions.update_lead_companies') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_lead_companies" name="delete_lead_companies" @if ($role->hasPermissionTo('delete_lead_companies')) checked @endif>
                                                <label for="delete_lead_companies">{{ __('permissions.delete_lead_companies') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_leads" name="get_leads" @if ($role->hasPermissionTo('get_leads')) checked @endif>
                                                <label for="get_leads">{{ __('permissions.get_leads') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_leads" name="create_leads" @if ($role->hasPermissionTo('create_leads')) checked @endif>
                                                <label for="create_leads">{{ __('permissions.create_leads') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_leads" name="update_leads" @if ($role->hasPermissionTo('update_leads')) checked @endif>
                                                <label for="update_leads">{{ __('permissions.update_leads') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_leads" name="delete_leads" @if ($role->hasPermissionTo('delete_leads')) checked @endif>
                                                <label for="delete_leads">{{ __('permissions.delete_leads') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_crm_funnel_types" name="get_crm_funnel_types" @if ($role->hasPermissionTo('get_crm_funnel_types')) checked @endif>
                                                <label for="get_crm_funnel_types">{{ __('permissions.get_crm_funnel_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_crm_funnel_types" name="create_crm_funnel_types" @if ($role->hasPermissionTo('create_crm_funnel_types')) checked @endif>
                                                <label for="create_crm_funnel_types">{{ __('permissions.create_crm_funnel_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_crm_funnel_types" name="update_crm_funnel_types" @if ($role->hasPermissionTo('update_crm_funnel_types')) checked @endif>
                                                <label for="update_crm_funnel_types">{{ __('permissions.update_crm_funnel_types') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_crm_funnel_types" name="delete_crm_funnel_types">
                                                <label for="delete_crm_funnel_types">{{ __('permissions.delete_crm_funnel_types') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Pós-vendas</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_follow_ups" name="get_company_follow_ups" @if ($role->hasPermissionTo('get_company_follow_ups')) checked @endif>
                                                <label for="get_company_follow_ups">{{ __('permissions.get_company_follow_ups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_follow_ups" name="create_company_follow_ups" @if ($role->hasPermissionTo('create_company_follow_ups')) checked @endif>
                                                <label for="create_company_follow_ups">{{ __('permissions.create_company_follow_ups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_follow_ups" name="update_company_follow_ups" @if ($role->hasPermissionTo('update_company_follow_ups')) checked @endif>
                                                <label for="update_company_follow_ups">{{ __('permissions.update_company_follow_ups') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_follow_ups" name="delete_company_follow_ups" @if ($role->hasPermissionTo('delete_company_follow_ups')) checked @endif>
                                                <label for="delete_company_follow_ups">{{ __('permissions.delete_company_follow_ups') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Operação</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_exams" name="get_exams" @if ($role->hasPermissionTo('get_exams')) checked @endif>
                                                <label for="get_exams">{{ __('permissions.get_exams') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_exams" name="create_exams" @if ($role->hasPermissionTo('create_exams')) checked @endif>
                                                <label for="create_exams">{{ __('permissions.create_exams') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_exams" name="update_exams" @if ($role->hasPermissionTo('update_exams')) checked @endif>
                                                <label for="update_exams">{{ __('permissions.update_exams') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_exams" name="delete_exams" @if ($role->hasPermissionTo('delete_exams')) checked @endif>
                                                <label for="delete_exams">{{ __('permissions.delete_exams') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_closing_glosses" name="get_company_closing_glosses" @if ($role->hasPermissionTo('get_company_closing_glosses')) checked @endif>
                                                <label for="get_company_closing_glosses">{{ __('permissions.get_company_closing_glosses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_closing_glosses" name="create_company_closing_glosses" @if ($role->hasPermissionTo('create_company_closing_glosses')) checked @endif>
                                                <label for="create_company_closing_glosses">{{ __('permissions.create_company_closing_glosses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_closing_glosses" name="update_company_closing_glosses" @if ($role->hasPermissionTo('update_company_closing_glosses')) checked @endif>
                                                <label for="update_company_closing_glosses">{{ __('permissions.update_company_closing_glosses') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_closing_glosses" name="delete_company_closing_glosses" @if ($role->hasPermissionTo('delete_company_closing_glosses')) checked @endif>
                                                <label for="delete_company_closing_glosses">{{ __('permissions.delete_company_closing_glosses') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_service_orders" name="get_service_orders" @if ($role->hasPermissionTo('get_service_orders')) checked @endif>
                                                <label for="get_service_orders">{{ __('permissions.get_service_orders') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_service_orders" name="create_service_orders" @if ($role->hasPermissionTo('create_service_orders')) checked @endif>
                                                <label for="create_service_orders">{{ __('permissions.create_service_orders') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_service_orders" name="update_service_orders" @if ($role->hasPermissionTo('update_service_orders')) checked @endif>
                                                <label for="update_service_orders">{{ __('permissions.update_service_orders') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_service_orders" name="delete_service_orders" @if ($role->hasPermissionTo('delete_service_orders')) checked @endif>
                                                <label for="delete_service_orders">{{ __('permissions.delete_service_orders') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Faturamento</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_company_closings" name="get_company_closings" @if ($role->hasPermissionTo('get_company_closings')) checked @endif>
                                                <label for="get_company_closings">{{ __('permissions.get_company_closings') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_company_closings" name="create_company_closings" @if ($role->hasPermissionTo('create_company_closings')) checked @endif>
                                                <label for="create_company_closings">{{ __('permissions.create_company_closings') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_company_closings" name="update_company_closings" @if ($role->hasPermissionTo('update_company_closings')) checked @endif>
                                                <label for="update_company_closings">{{ __('permissions.update_company_closings') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_company_closings" name="delete_company_closings" @if ($role->hasPermissionTo('delete_company_closings')) checked @endif>
                                                <label for="delete_company_closings">{{ __('permissions.delete_company_closings') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_stand_alone_contracts_for_billing" name="get_stand_alone_contracts_for_billing" @if ($role->hasPermissionTo('get_stand_alone_contracts_for_billing')) checked @endif>
                                                <label for="get_stand_alone_contracts_for_billing">{{ __('permissions.get_stand_alone_contracts_for_billing') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Financeiro</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_receivables" name="get_receivables" @if ($role->hasPermissionTo('get_receivables')) checked @endif>
                                                <label for="get_receivables">{{ __('permissions.get_receivables') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_receivables" name="update_receivables" @if ($role->hasPermissionTo('update_receivables')) checked @endif>
                                                <label for="update_receivables">{{ __('permissions.update_receivables') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Base de conhecimento</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_articles" name="get_articles" @if ($role->hasPermissionTo('get_articles')) checked @endif>
                                                <label for="get_articles">{{ __('permissions.get_articles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_articles" name="create_articles" @if ($role->hasPermissionTo('create_articles')) checked @endif>
                                                <label for="create_articles">{{ __('permissions.create_articles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_articles" name="update_articles" @if ($role->hasPermissionTo('update_articles')) checked @endif>
                                                <label for="update_articles">{{ __('permissions.update_articles') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_articles" name="delete_articles" @if ($role->hasPermissionTo('delete_articles')) checked @endif>
                                                <label for="delete_articles">{{ __('permissions.delete_articles') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_article_categories" name="get_article_categories" @if ($role->hasPermissionTo('get_article_categories')) checked @endif>
                                                <label for="get_article_categories">{{ __('permissions.get_article_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_article_categories" name="create_article_categories" @if ($role->hasPermissionTo('create_article_categories')) checked @endif>
                                                <label for="create_article_categories">{{ __('permissions.create_article_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_article_categories" name="update_article_categories" @if ($role->hasPermissionTo('update_article_categories')) checked @endif>
                                                <label for="update_article_categories">{{ __('permissions.update_article_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_article_categories" name="delete_article_categories" @if ($role->hasPermissionTo('delete_article_categories')) checked @endif>
                                                <label for="delete_article_categories">{{ __('permissions.delete_article_categories') }}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_article_tags" name="get_article_tags" @if ($role->hasPermissionTo('get_article_tags')) checked @endif>
                                                <label for="get_article_tags">{{ __('permissions.get_article_tags') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_article_tags" name="create_article_tags" @if ($role->hasPermissionTo('create_article_tags')) checked @endif>
                                                <label for="create_article_tags">{{ __('permissions.create_article_tags') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_article_tags" name="update_article_tags" @if ($role->hasPermissionTo('update_article_tags')) checked @endif>
                                                <label for="update_article_tags">{{ __('permissions.update_article_tags') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_article_tags" name="delete_article_tags" @if ($role->hasPermissionTo('delete_article_tags')) checked @endif>
                                                <label for="delete_article_tags">{{ __('permissions.delete_article_tags') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <strong>Chamados</strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="get_ticket_categories" name="get_ticket_categories" @if ($role->hasPermissionTo('get_ticket_categories')) checked @endif>
                                                <label for="get_ticket_categories">{{ __('permissions.get_ticket_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="create_ticket_categories" name="create_ticket_categories" @if ($role->hasPermissionTo('create_ticket_categories')) checked @endif>
                                                <label for="create_ticket_categories">{{ __('permissions.create_ticket_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="update_ticket_categories" name="update_ticket_categories" @if ($role->hasPermissionTo('update_ticket_categories')) checked @endif>
                                                <label for="update_ticket_categories">{{ __('permissions.update_ticket_categories') }}</label>
                                            </div>
                                            <div class="col-sm-12 col-md-3 col-lg-3">
                                                <input type="checkbox" id="delete_ticket_categories" name="delete_ticket_categories" @if ($role->hasPermissionTo('delete_ticket_categories')) checked @endif>
                                                <label for="delete_ticket_categories">{{ __('permissions.delete_ticket_categories') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="card-header d-flex align-items-center">
                                <strong>Relatórios</strong>
                            </div>
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_commission_payment_report" name="get_commission_payment_report" @if ($role->hasPermissionTo('get_commission_payment_report')) checked @endif>
                                            <label for="get_commission_payment_report">{{ __('permissions.get_commission_payment_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_companies_report" name="get_companies_report" @if ($role->hasPermissionTo('get_companies_report')) checked @endif>
                                            <label for="get_companies_report">{{ __('permissions.get_companies_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_companies_with_life_count_report" name="get_companies_with_life_count_report" @if ($role->hasPermissionTo('get_companies_with_life_count_report')) checked @endif>
                                            <label for="get_companies_with_life_count_report">{{ __('permissions.get_companies_with_life_count_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_providers_report" name="get_providers_report" @if ($role->hasPermissionTo('get_providers_report')) checked @endif>
                                            <label for="get_providers_report">{{ __('permissions.get_providers_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_provider_companies_report" name="get_provider_companies_report" @if ($role->hasPermissionTo('get_provider_companies_report')) checked @endif>
                                            <label for="get_provider_companies_report">{{ __('permissions.get_provider_companies_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_provider_procedures_report" name="get_provider_procedures_report" @if ($role->hasPermissionTo('get_provider_procedures_report')) checked @endif>
                                            <label for="get_provider_procedures_report">{{ __('permissions.get_provider_procedures_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_suppliers_report" name="get_suppliers_report" @if ($role->hasPermissionTo('get_suppliers_report')) checked @endif>
                                            <label for="get_suppliers_report">{{ __('permissions.get_suppliers_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_contracts_report" name="get_contracts_report" @if ($role->hasPermissionTo('get_contracts_report')) checked @endif>
                                            <label for="get_contracts_report">{{ __('permissions.get_contracts_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_contracts_without_service_orders_report" name="get_contracts_without_service_orders_report" @if ($role->hasPermissionTo('get_contracts_without_service_orders_report')) checked @endif>
                                            <label for="get_contracts_without_service_orders_report">{{ __('permissions.get_contracts_without_service_orders_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_pending_billing_contracts_for_period_report" name="get_pending_billing_contracts_for_period_report" @if ($role->hasPermissionTo('get_pending_billing_contracts_for_period_report')) checked @endif>
                                            <label for="get_pending_billing_contracts_for_period_report">{{ __('permissions.get_pending_billing_contracts_for_period_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_service_orders_report" name="get_service_orders_report" @if ($role->hasPermissionTo('get_service_orders_report')) checked @endif>
                                            <label for="get_service_orders_report">{{ __('permissions.get_service_orders_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_open_receivables_report" name="get_open_receivables_report" @if ($role->hasPermissionTo('get_open_receivables_report')) checked @endif>
                                            <label for="get_open_receivables_report">{{ __('permissions.get_open_receivables_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_provider_expenses_report" name="get_provider_expenses_report" @if ($role->hasPermissionTo('get_provider_expenses_report')) checked @endif>
                                            <label for="get_provider_expenses_report">{{ __('permissions.get_provider_expenses_report') }}</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="get_supplier_expenses_report" name="get_supplier_expenses_report" @if ($role->hasPermissionTo('get_supplier_expenses_report')) checked @endif>
                                            <label for="get_supplier_expenses_report">{{ __('permissions.get_supplier_expenses_report') }}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="card-header d-flex align-items-center">
                                <strong>Visibilidade em caixas de seleção</strong>
                            </div>
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="shows_in_salesman_dropdowns" name="shows_in_salesman_dropdowns" @if ($role->shows_in_salesman_dropdowns) checked @endif>
                                            <label for="shows_in_salesman_dropdowns">Vendas</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="shows_in_after_sales_dropdowns" name="shows_in_after_sales_dropdowns" @if ($role->shows_in_after_sales_dropdowns) checked @endif>
                                            <label for="shows_in_after_sales_dropdowns">Pós-vendas</label>
                                        </div>
                                        <div class="col-sm-12 col-md-12 col-lg-12">
                                            <input type="checkbox" id="shows_in_technician_dropdowns" name="shows_in_technician_dropdowns" @if ($role->shows_in_technician_dropdowns) checked @endif>
                                            <label for="shows_in_technician_dropdowns">Técnico</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
@endsection
