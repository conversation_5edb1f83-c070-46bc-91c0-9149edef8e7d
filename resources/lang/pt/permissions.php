<?php

return [

    'get_roles' => 'Listar perfis',
    'create_roles' => 'Criar perfis',
    'update_roles' => 'Editar perfis',
    'delete_roles' => 'Excluir perfis',

    'get_operators' => 'Listar operadores',
    'create_operators' => 'Criar operadores',
    'update_operators' => 'Editar operadores',
    'delete_operators' => 'Excluir operadores',

    'get_holidays' => 'Listar feriados',
    'create_holidays' => 'Criar feriados',
    'update_holidays' => 'Editar feriados',
    'delete_holidays' => 'Excluir feriados',

    'get_indices' => 'Listar índices',
    'create_indices' => 'Criar índices',
    'update_indices' => 'Editar índices',
    'delete_indices' => 'Excluir índices',

    'get_procedures' => 'Listar procedimentos',
    'create_procedures' => 'Criar procedimentos',
    'update_procedures' => 'Editar procedimentos',
    'delete_procedures' => 'Excluir procedimentos',

    'get_expense_types' => 'Listar tipos de despesas',
    'create_expense_types' => 'Criar tipos de despesas',
    'update_expense_types' => 'Editar tipos de despesas',
    'delete_expense_types' => 'Excluir tipos de despesas',

    'get_contract_types' => 'Listar tipos de contratos',
    'create_contract_types' => 'Criar tipos de contratos',
    'update_contract_types' => 'Editar tipos de contratos',
    'delete_contract_types' => 'Excluir tipos de contratos',

    'get_cancellation_reasons' => 'Listar motivos de cancelamento',
    'create_cancellation_reasons' => 'Criar motivos de cancelamento',
    'update_cancellation_reasons' => 'Editar motivos de cancelamento',
    'delete_cancellation_reasons' => 'Excluir motivos de cancelamento',

    'get_companies' => 'Listar clientes',
    'create_companies' => 'Criar clientes',
    'update_companies' => 'Editar clientes',
    'delete_companies' => 'Excluir clientes',

    'get_company_contacts' => 'Listar contatos de clientes',
    'create_company_contacts' => 'Criar contatos de clientes',
    'update_company_contacts' => 'Editar contatos de clientes',
    'delete_company_contacts' => 'Excluir contatos de clientes',

    'get_company_groups' => 'Listar grupos empresariais',
    'create_company_groups' => 'Criar grupos empresariais',
    'update_company_groups' => 'Editar grupos empresariais',
    'delete_company_groups' => 'Excluir grupos empresariais',

    'get_company_group_companies' => 'Listar clientes de grupos empresariais',
    'create_company_group_companies' => 'Criar clientes de grupos empresariais',
    'update_company_group_companies' => 'Editar clientes de grupos empresariais',
    'delete_company_group_companies' => 'Excluir clientes de grupos empresariais',

    'get_suppliers' => 'Listar fornecedores',
    'create_suppliers' => 'Criar fornecedores',
    'update_suppliers' => 'Editar fornecedores',
    'delete_suppliers' => 'Excluir fornecedores',

    'get_supplier_expense_types' => 'Listar tipos de despesas de fornecedores',
    'create_supplier_expense_types' => 'Criar tipos de despesas de fornecedores',
    'update_supplier_expense_types' => 'Editar tipos de despesas de fornecedores',
    'delete_supplier_expense_types' => 'Excluir tipos de despesas de fornecedores',

    'get_providers' => 'Listar credenciados',
    'create_providers' => 'Criar credenciados',
    'update_providers' => 'Editar credenciados',
    'delete_providers' => 'Excluir credenciados',

    'get_provider_contacts' => 'Listar contatos de credenciados',
    'create_provider_contacts' => 'Criar contatos de credenciados',
    'update_provider_contacts' => 'Editar contatos de credenciados',
    'delete_provider_contacts' => 'Excluir contatos de credenciados',

    'get_provider_files' => 'Listar arquivos de credenciados',
    'create_provider_files' => 'Criar arquivos de credenciados',
    'update_provider_files' => 'Editar arquivos de credenciados',
    'delete_provider_files' => 'Excluir arquivos de credenciados',

    'get_provider_companies' => 'Listar clientes de credenciados',
    'create_provider_companies' => 'Criar clientes de credenciados',
    'update_provider_companies' => 'Editar clientes de credenciados',
    'delete_provider_companies' => 'Excluir clientes de credenciados',

    'get_workflows' => 'Listar workflows',
    'create_workflows' => 'Criar workflows',
    'update_workflows' => 'Editar workflows',
    'delete_workflows' => 'Excluir workflows',

    'get_schedules' => 'Listar agenda',
    'create_schedules' => 'Criar agenda',
    'update_schedules' => 'Editar agenda',
    'delete_schedules' => 'Excluir agenda',

    'get_contracts' => 'Listar contratos',
    'create_contracts' => 'Criar contratos',
    'update_contracts' => 'Editar contratos',
    'delete_contracts' => 'Excluir contratos',

    'get_provider_procedures' => 'Listar procedimentos de credenciados',
    'create_provider_procedures' => 'Criar procedimentos de credenciados',
    'update_provider_procedures' => 'Editar procedimentos de credenciados',
    'delete_provider_procedures' => 'Excluir procedimentos de credenciados',

    'get_supplier_expenses' => 'Listar despesas de fornecedores',
    'create_supplier_expenses' => 'Criar despesas de fornecedores',
    'update_supplier_expenses' => 'Editar despesas de fornecedores',
    'delete_supplier_expenses' => 'Excluir despesas de fornecedores',
    'approve_supplier_expenses' => 'Aprovar despesas de fornecedores',

    'get_provider_expenses' => 'Listar despesas de credenciados',
    'create_provider_expenses' => 'Criar despesas de credenciados',
    'update_provider_expenses' => 'Editar despesas de credenciados',
    'delete_provider_expenses' => 'Excluir despesas de credenciados',
    'approve_provider_expenses' => 'Aprovar despesas de credenciados',

    'get_provider_city_coverage_cities' => 'Listar abrangência (cidades) de credenciados',
    'create_provider_city_coverage_cities' => 'Criar abrangência (cidades) de credenciados',
    'update_provider_city_coverage_cities' => 'Editar abrangência (cidades) de credenciados',
    'delete_provider_city_coverage_cities' => 'Excluir abrangência (cidades) de credenciados',

    'get_exams' => 'Listar exames',
    'create_exams' => 'Criar exames',
    'update_exams' => 'Editar exames',
    'delete_exams' => 'Excluir exames',

    'get_company_closing_glosses' => 'Listar glosas',
    'create_company_closing_glosses' => 'Criar glosas',
    'update_company_closing_glosses' => 'Editar glosas',
    'delete_company_closing_glosses' => 'Excluir glosas',

    'get_receivables' => 'Listar contas a receber',
    'update_receivables' => 'Editar conta a receber',

    'get_service_orders' => 'Listar ordens de serviço',
    'create_service_orders' => 'Criar ordens de serviço',
    'update_service_orders' => 'Editar ordens de serviço',
    'delete_service_orders' => 'Excluir ordens de serviço',

    'get_company_closings' => 'Listar faturamento (recorrência)',
    'create_company_closings' => 'Criar faturamento (recorrência)',
    'update_company_closings' => 'Editar faturamento (recorrência)',
    'delete_company_closings' => 'Excluir faturamento (recorrência)',

    'get_pre_companies' => 'Listar pré-cadastros de empresas',
    'create_pre_companies' => 'Criar pré-cadastros de empresas',
    'update_pre_companies' => 'Editar pré-cadastros de empresas',
    'delete_pre_companies' => 'Excluir pré-cadastros de empresas',

    'get_checklists' => 'Listar checklists',
    'create_checklists' => 'Criar checklists',
    'update_checklists' => 'Editar checklists',
    'delete_checklists' => 'Excluir checklists',

    'get_checklist_phases' => 'Listar fases de checklists',
    'create_checklist_phases' => 'Criar fases de checklists',
    'update_checklist_phases' => 'Editar fases de checklists',
    'delete_checklist_phases' => 'Excluir fases de checklists',

    'get_checklist_phase_items' => 'Listar itens de fases de checklists',
    'create_checklist_phase_items' => 'Criar itens de fases de checklists',
    'update_checklist_phase_items' => 'Editar itens de fases de checklists',
    'delete_checklist_phase_items' => 'Excluir itens de fases de checklists',

    'get_crm_city_coverages' => 'Listar abrangências (CRM)',
    'create_crm_city_coverages' => 'Criar abrangências (CRM)',
    'update_crm_city_coverages' => 'Editar abrangências (CRM)',
    'delete_crm_city_coverages' => 'Excluir abrangências (CRM)',

    'get_crm_city_coverage_cities' => 'Listar cidades de abrangências (CRM)',
    'create_crm_city_coverage_cities' => 'Criar cidades de abrangências (CRM)',
    'update_crm_city_coverage_cities' => 'Editar cidades de abrangências (CRM)',
    'delete_crm_city_coverage_cities' => 'Excluir cidades de abrangências (CRM)',

    'get_procedure_crm_city_coverages' => 'Listar abrangências de procedimentos',
    'create_procedure_crm_city_coverages' => 'Criar abrangências de procedimentos',
    'update_procedure_crm_city_coverages' => 'Editar abrangências de procedimentos',
    'delete_procedure_crm_city_coverages' => 'Excluir abrangências de procedimentos',

    'get_procedure_crm_city_coverage_ranges' => 'Listar faixas de abrangências de procedimentos',
    'create_procedure_crm_city_coverage_ranges' => 'Criar faixas de abrangências de procedimentos',
    'update_procedure_crm_city_coverage_ranges' => 'Editar faixas de abrangências de procedimentos',
    'delete_procedure_crm_city_coverage_ranges' => 'Excluir faixas de abrangências de procedimentos',

    'get_crm_funnels' => 'Listar funis',
    'create_crm_funnels' => 'Criar funis',
    'update_crm_funnels' => 'Editar funis',
    'delete_crm_funnels' => 'Excluir funis',

    'get_crm_funnel_steps' => 'Listar passos de funis',
    'create_crm_funnel_steps' => 'Criar passos de funis',
    'update_crm_funnel_steps' => 'Editar passos de funis',
    'delete_crm_funnel_steps' => 'Excluir passos de funis',

    'get_crm_funnel_operators' => 'Listar operadores de funis',
    'create_crm_funnel_operators' => 'Criar operadores de funis',
    'update_crm_funnel_operators' => 'Editar operadores de funis',
    'delete_crm_funnel_operators' => 'Excluir operadores de funis',

    'get_lead_companies' => 'Listar empresas',
    'create_lead_companies' => 'Criar empresas',
    'update_lead_companies' => 'Editar empresas',
    'delete_lead_companies' => 'Excluir empresas',

    'get_leads' => 'Listar leads',
    'create_leads' => 'Criar leads',
    'update_leads' => 'Editar leads',
    'delete_leads' => 'Excluir leads',

    'get_crm_funnel_types' => 'Listar tipos de funis',
    'create_crm_funnel_types' => 'Criar tipos de funis',
    'update_crm_funnel_types' => 'Editar tipos de funis',
    'delete_crm_funnel_types' => 'Excluir tipos de funis',

    'get_company_follow_ups' => 'Listar follow-ups de clientes',
    'create_company_follow_ups' => 'Criar follow-ups de clientes',
    'update_company_follow_ups' => 'Editar follow-ups de clientes',
    'delete_company_follow_ups' => 'Excluir follow-ups de clientes',

    'get_procedure_payment_methods' => 'Listar formas de pagamentos (procedimentos)',
    'create_procedure_payment_methods' => 'Criar formas de pagamentos (procedimentos)',
    'update_procedure_payment_methods' => 'Editar formas de pagamentos (procedimentos)',
    'delete_procedure_payment_methods' => 'Excluir formas de pagamentos (procedimentos)',

    'get_reasons' => 'Listar motivos',
    'create_reasons' => 'Criar motivos',
    'update_reasons' => 'Editar motivos',
    'delete_reasons' => 'Excluir motivos',

    'get_article_categories' => 'Listar categorias de artigos/procedimentos',
    'create_article_categories' => 'Criar categorias de artigos/procedimentos',
    'update_article_categories' => 'Editar categorias de artigos/procedimentos',
    'delete_article_categories' => 'Excluir categorias de artigos/procedimentos',

    'get_article_tags' => 'Listar tags de artigos/procedimentos',
    'create_article_tags' => 'Criar tags de artigos/procedimentos',
    'update_article_tags' => 'Editar tags de artigos/procedimentos',
    'delete_article_tags' => 'Excluir tags de artigos/procedimentos',

    'get_articles' => 'Listar artigos/procedimentos',
    'create_articles' => 'Criar artigos/procedimentos',
    'update_articles' => 'Editar artigos/procedimentos',
    'delete_articles' => 'Excluir artigos/procedimentos',

    'get_ticket_categories' => 'Listar categorias de chamados',
    'create_ticket_categories' => 'Criar categorias de chamados',
    'update_ticket_categories' => 'Editar categorias de chamados',
    'delete_ticket_categories' => 'Excluir categorias de chamados',

    'get_tickets' => 'Listar chamados',
    'create_tickets' => 'Criar chamados',
    'update_tickets' => 'Editar chamados',
    'delete_tickets' => 'Excluir chamados',

    'get_stand_alone_contracts_for_billing' => 'Listar faturamento (parcelados)',

    'get_contracts_report' => 'Contratos',
    'get_contracts_without_service_orders_report' => 'Contratos sem OS (+7 dias)',
    'get_service_orders_report' => 'Ordens de serviço',
    'get_commission_payment_report' => 'Pagamento de comissões',
    'get_companies_report' => 'Clientes',
    'get_providers_report' => 'Credenciados',
    'get_suppliers_report' => 'Fornecedores',
    'get_companies_with_life_count_report' => 'Clientes com quantidade de vidas',
    'get_company_loss_ratio_report' => 'Sinistralidade de exames por cliente',
    'get_provider_loss_ratio_report' => 'Sinistralidade de exames por credenciado',
    'get_provider_procedures_report' => 'Exames dos credenciados',
    'get_provider_companies_report' => 'Credenciados x clientes',
    'get_pending_billing_contracts_for_period_report' => 'Contratos pendentes de faturamento no período',
    'get_open_receivables_report' => 'Contas a receber',
    'get_provider_expenses_report' => 'Despesas (credenciados)',
    'get_supplier_expenses_report' => 'Despesas (fornecedores)',

    'create_employee_inactivity_import' => 'Quadro de funcionários'

];
